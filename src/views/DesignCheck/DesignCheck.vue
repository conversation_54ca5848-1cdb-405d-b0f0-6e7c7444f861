<script setup lang="ts">
import type { TextLayer } from '@/api/types'
import { saveDesign } from '@/api/imageTranslationApi'
import { LayerType } from '@/api/types'
import ContentSection from '@/components/ContentSection/ContentSection.vue'
import DesignThumbnailList from '@/components/DesignThumbnailList/DesignThumbnailList.vue'
import ImageTag from '@/components/ImageTag/ImageTag.vue'
import MarkerList from '@/components/MarkerList/MarkerList.vue'
import { useDesignViewer } from '@/composables/useDesignViewer'
import { useThumbnailSelection } from '@/composables/useThumbnailSelection'
import { useDesignStore } from '@/stores/design'
import { useImagesStore } from '@/stores/images'
import { ElMessage } from 'element-plus'
import { fabric } from 'fabric'
import { useI18n } from 'vue-i18n'

const currentTag = ref('mainImg')
const showContentSection = ref(false)
const { t } = useI18n()

// 使用共享的设计稿查看器组合式函数
const {
  canvasEditor,
  currentImageType,
  highlightMarker,
  highlightedMarkerId,
} = useDesignViewer(currentTag)

const imagesStore = useImagesStore()
const designStore = useDesignStore()

// 定义标记接口
interface Marker {
  id: string
  rect: fabric.Object
  content: string
}

// 定义缓存标记接口，不包含 fabric 对象
interface CachedMarker {
  id: string
  content: string
  left: number
  top: number
  width: number
  height: number
}

const markers = ref<Marker[]>([])
const nextMarkerId = ref(1)
// 本地缓存每个图片的标记数据
const markersCache = ref<Record<string, CachedMarker[]>>({})

// 获取矩形的真实包围盒信息
function rectInfo(rect: fabric.Rect) {
  // 精确到像素，获取绝对位置和尺寸
  const { left, top, width, height } = rect.getBoundingRect(true /* absolute */)
  return { left, top, width, height }
}

// 导出标记数据为纯数据格式
function dumpMarkers(): CachedMarker[] {
  return markers.value.map(m => ({
    id: m.id,
    content: m.content,
    ...rectInfo(m.rect as fabric.Rect),
  }))
}

provide('saveMarkers', saveMarkers)

// 使用缩略图选择组合式函数
const {
  handleThumbnailSelected,
  prevImage: handlePrevImage,
  nextImage: handleNextImage,
  previousImageId,
} = useThumbnailSelection(currentImageType, {
  // 切换前保存标记
  beforeImageChange: async (imageId) => {
    if (markers.value.length > 0) {
      // 保存到本地缓存
      markersCache.value[imageId] = dumpMarkers()
      // 保存到后端
      await saveMarkers(false, false)
    }
    return true
  },
  // 切换后清除标记
  afterImageChange: () => {
    clearAllMarkers()
  },
})

// 监听当前图片类型变化
watch(
  () => currentImageType.value,
  (newType) => {
    if (imagesStore.transId) {
      designStore.fetchDesignInfo(newType, imagesStore.transId)
    }
  },
)

// 监听对象修改事件，同步最新几何信息
onMounted(() => {
  nextTick(() => {
    canvasEditor.fabricCanvas?.on('object:modified', (e) => {
      const obj = e.target as (fabric.Rect & { id: string })
      if (!obj?.id)
        return
      const m = markers.value.find(mk => mk.id === obj.id)
      if (m)
        m.rect = obj // 保持引用
    })
  })
})

// 监听 activeImageId 变化，在切换图片时自动保存标记
watch(
  () => imagesStore.activeImageId,
  async (newId, oldId) => {
    // 如果是通过缩略图列表点击切换图片，则自动保存标记
    if (oldId && oldId !== '0' && previousImageId.value === oldId && markers.value.length > 0) {
      // 保存前一张图片的标记
      markersCache.value[oldId] = dumpMarkers()
      const tempActiveImageId = imagesStore.activeImageId
      imagesStore.activeImageId = oldId
      await saveMarkers(false, false)
      imagesStore.activeImageId = tempActiveImageId
    }

    // 如果新图片在缓存中有标记数据，则优先使用缓存数据
    if (newId && newId !== '0') {
      if (markersCache.value[newId] && markersCache.value[newId].length > 0) {
        // 使用缓存数据
        nextTick(() => {
          restoreMarkersFromCache(newId)
        })
      }
      else {
        // 如果缓存中没有数据，则尝试从设计稿信息中加载标记
        const designs = designStore.designInfoMap[currentImageType.value] || []
        const currentDesign = designs.find(d => d.designId === newId)

        if (currentDesign && currentDesign.remark) {
          try {
            // 尝试解析标记数据
            const parsedLayers = JSON.parse(currentDesign.remark)
            if (Array.isArray(parsedLayers) && parsedLayers.length > 0) {
              // 清除当前标记
              clearAllMarkers()

              // 初始化 nextMarkerId
              let maxId = 0

              // 从设计稿信息中创建标记
              parsedLayers.forEach((layer, index) => {
                if (!canvasEditor.fabricCanvas)
                  return

                const markerId = `marker-${index + 1}`
                maxId = Math.max(maxId, index + 1)

                // 创建标记矩形
                const rect = new fabric.Rect({
                  left: layer.left || 0,
                  top: layer.top || 0,
                  width: layer.width || 100,
                  height: layer.height || 100,
                  fill: 'rgba(255, 0, 0, 0.2)',
                  stroke: '#ff0000',
                  strokeWidth: 2,
                  cornerColor: '#ff0000',
                  transparentCorners: false,
                  strokeUniform: true,
                }) as fabric.Rect & { id: string }

                // 设置 ID
                rect.id = markerId

                // 添加到画布
                canvasEditor.fabricCanvas.add(rect)

                // 添加到标记列表
                markers.value.push({
                  id: markerId,
                  rect,
                  content: layer.remark || '',
                })
              })

              // 更新 nextMarkerId
              nextMarkerId.value = maxId + 1

              // 更新缓存
              if (markers.value.length > 0) {
                markersCache.value[newId] = dumpMarkers()
              }

              // 重新渲染画布
              canvasEditor.fabricCanvas?.renderAll()
            }
          }
          catch (error) {
            console.error('解析设计稿标记数据失败:', error)
          }
        }
      }
    }

    previousImageId.value = newId
  },
  {
    immediate: true,
  },
)

// 切换到上一个设计稿
async function prevImage() {
  await handlePrevImage()
}

// 切换到下一个设计稿
async function nextImage() {
  await handleNextImage()
}

function addMarker() {
  if (!canvasEditor.fabricCanvas || !imagesStore.activeImageId) {
    return
  }

  const canvas = canvasEditor.fabricCanvas
  const canvasWidth = canvas.getWidth()
  const canvasHeight = canvas.getHeight()

  // 创建一个标记框（矩形）
  const markerId = `marker-${nextMarkerId.value}`
  nextMarkerId.value++

  // 在画布中心创建一个标记框
  const rect = new fabric.Rect({
    left: canvasWidth / 2 - 50,
    top: canvasHeight / 2 - 50,
    width: 100,
    height: 100,
    fill: 'rgba(255, 0, 0, 0.2)',
    stroke: '#ff0000',
    strokeWidth: 2,
    cornerColor: '#ff0000',
    transparentCorners: false,
    strokeUniform: true, // 关键属性：使边框宽度不受缩放影响
  }) as fabric.Rect & { id: string }

  // 手动设置 ID
  rect.id = markerId

  canvas.add(rect)
  canvas.setActiveObject(rect)

  // 添加到标记列表
  markers.value.push({
    id: markerId,
    rect,
    content: '',
  })

  // 更新本地缓存
  if (imagesStore.activeImageId) {
    markersCache.value[imagesStore.activeImageId] = dumpMarkers()
  }
}

// 批量清除标记
function clearAllMarkers(deleteBackground = false) {
  if (!canvasEditor.fabricCanvas) {
    return
  }

  const canvas = canvasEditor.fabricCanvas

  // 移除所有标记
  const objects = canvas.getObjects()
  objects.forEach((obj: any) => {
    if (deleteBackground) {
      // 删除除了工作区以外的所有对象
      if (obj.id !== 'workspace') {
        canvas && canvas.remove(obj)
      }
    }
    else {
      // 只删除标记，保留背景图
      if (obj.id !== 'workspace' && obj.type !== 'image') {
        canvas && canvas.remove(obj)
      }
    }
  })

  // 清空标记列表
  markers.value = []
}

// 清除单个标记
function clearMarker(markerId: string) {
  if (!canvasEditor.fabricCanvas) {
    return
  }

  const canvas = canvasEditor.fabricCanvas
  const markerIndex = markers.value.findIndex(m => m.id === markerId)

  if (markerIndex !== -1) {
    canvas.remove(markers.value[markerIndex].rect)
    markers.value.splice(markerIndex, 1)

    // 更新本地缓存
    if (imagesStore.activeImageId) {
      if (markers.value.length > 0) {
        markersCache.value[imagesStore.activeImageId] = dumpMarkers()
      }
      else {
        delete markersCache.value[imagesStore.activeImageId]
      }
    }

    // 重新渲染画布以更新显示
    canvas.renderAll()
  }
}

// 从缓存中恢复标记
function restoreMarkersFromCache(imageId: string) {
  if (!canvasEditor.fabricCanvas || !markersCache.value[imageId]) {
    return
  }

  // 清除当前标记
  clearAllMarkers()

  // 从缓存中恢复标记
  const cachedMarkers = markersCache.value[imageId]

  cachedMarkers.forEach((markerData: CachedMarker) => {
    // 创建新的标记矩形
    const canvas = canvasEditor.fabricCanvas
    if (!canvas)
      return

    const rect = new fabric.Rect({
      left: markerData.left,
      top: markerData.top,
      width: markerData.width,
      height: markerData.height,
      fill: 'rgba(255, 0, 0, 0.2)',
      stroke: '#ff0000',
      strokeWidth: 2,
      cornerColor: '#ff0000',
      transparentCorners: false,
      strokeUniform: true,
    }) as fabric.Rect & { id: string }

    // 设置 ID
    rect.id = markerData.id

    // 添加到画布
    canvas.add(rect)

    // 添加到标记列表
    markers.value.push({
      id: markerData.id,
      rect,
      content: markerData.content,
    })
  })

  // 更新 nextMarkerId
  if (markers.value.length > 0) {
    const maxId = Math.max(...markers.value.map(m => Number.parseInt(m.id.split('-')[1])))
    nextMarkerId.value = maxId + 1
  }

  // 重新渲染画布
  canvasEditor.fabricCanvas?.renderAll()
}

// 保存设计备注
async function saveMarkers(showValidationMessage = true, showSuccessMessage = true) {
  if (!imagesStore.activeImageId) {
    return false
  }

  // 如果没有标记，直接返回成功
  if (markers.value.length === 0) {
    return true
  }

  // 检查是否有空的标记内容
  const emptyMarkers = markers.value.filter(m => !m.content.trim())
  if (emptyMarkers.length > 0) {
    if (showValidationMessage) {
      ElMessage.warning(t('page.design_check.pleaseEnterRemark'))
    }
    return false
  }

  try {
    // 更新本地缓存
    markersCache.value[imagesStore.activeImageId] = dumpMarkers()

    const textLayers: TextLayer[] = markers.value.map((marker, index) => {
      // 获取标记矩形的真实位置和大小
      const { left, top, width, height } = rectInfo(marker.rect as fabric.Rect)
      return {
        id: Number(marker.id.split('-')[1]),
        top,
        left,
        width,
        height,
        label: 'text',
        remark: marker.content.trim(), // 将标记内容保存到 remark 字段
        zIndex: index,
        type: LayerType.TEXT,
        backgroundColor: 'transparent',
        color: '#ff0000',
        textAlign: 'left',
        letterSpacing: 0,
        fontFamily: 'Arial',
        fontSize: 14,
        lineHeight: 1.2,
        direction: null,
        content: '',
        ocrContent: '',
        stroke: '#ff0000',
        strokeWidth: 2,
        fill: 'rgba(255, 0, 0, 0.2)',
        borderRadius: 0,
      }
    })

    const remarkContent = JSON.stringify(textLayers)

    // 获取当前设计图片的链接
    const designs = designStore.designInfoMap[currentImageType.value] || []
    const currentDesign = designs.find(d => d.designId === imagesStore.activeImageId)
    const designLink = currentDesign?.designLink || ''

    await saveDesign({
      designId: imagesStore.activeImageId,
      designLink,
      remark: remarkContent,
    })

    if (showSuccessMessage) {
      ElMessage.success(t('upload.operationSuccess'))
    }
    return true
  }
  catch (error) {
    console.error('保存标记失败:', error)
    return false
  }
}
</script>

<template>
  <div class="design-check">
    <ImageTag v-model="currentTag" @content-selected="showContentSection = true" />
    <div class="workspace-container">
      <!-- 左侧缩略图列表 -->
      <div v-show="currentTag !== 'content'" class="thumbnail-container">
        <DesignThumbnailList :current-type="currentImageType" @thumbnail-selected="handleThumbnailSelected" />
      </div>
      <!-- 文案部分 -->
      <ContentSection v-show="currentTag === 'content'" />
      <!-- 主要内容区域 -->
      <div v-show="currentTag !== 'content'" class="main-content">
        <div class="control-buttons">
          <el-button @click="prevImage">
            {{ t('page.design_check.prev_image') }}
          </el-button>
          <el-button @click="nextImage">
            {{ t('page.design_check.next_image') }}
          </el-button>
          <el-button @click="addMarker">
            {{ t('page.design_check.add_marker') }}
          </el-button>
          <el-button @click="() => clearAllMarkers(false)">
            {{ t('page.design_check.batch_clear') }}
          </el-button>
        </div>
        <div v-loading="!imagesStore.activeImageId" class="content-area">
          <!-- 画布区域 -->
          <div class="canvas-section">
            <div id="workspace">
              <div class="canvas-box">
                <div class="inside-shadow" />
                <canvas id="canvas" />
              </div>
            </div>
          </div>
          <!-- 标记列表 -->
          <MarkerList
            :markers="markers" :editable="true" :highlighted-marker-id="highlightedMarkerId"
            @highlight-marker="highlightMarker" @clear-marker="clearMarker"
            @update:markers="(newMarkers) => markers = newMarkers"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.design-check {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.workspace-container {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.thumbnail-container {
  min-width: 14vw;
  width: 14vw;
  border-right: 1px solid #e0e0e0;
  overflow-y: hidden;
  padding: 16px;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.control-buttons {
  padding: 10px;
  display: flex;
  gap: 10px;
  border-bottom: 1px solid #eee;
}

.content-area {
  flex: 1;
  display: flex;
  height: 100%;
}

.canvas-section {
  flex: 1;
  display: flex;
  justify-content: center;
  height: 100%;
  width: 100%;
  overflow: hidden;
}

#workspace {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #f5f5f5;
  overflow: hidden;
}

.canvas-box {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.inside-shadow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

#canvas {
  position: absolute !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
}
</style>

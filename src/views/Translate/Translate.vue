<script setup lang="ts">
import type { SaveImageLayersReq, TextLayer } from '@/api/types'
import { saveImageLayers, TranslateStatus, uploadImage } from '@/api/imageTranslationApi'
// ImageType is used through getImageTypeFromTag
import AITranslateDialog from '@/components/AITranslateDialog/AITranslateDialog.vue'
import ContentSection from '@/components/ContentSection/ContentSection.vue'
import ImageTag from '@/components/ImageTag/ImageTag.vue'
import ThumbnailList from '@/components/ThumbnailList/ThumbnailList.vue'
import { useCanvas } from '@/composables/useCanvas'
import { useEditorPlugins } from '@/composables/useEditorPlugins'
import { useImageChangeGuard } from '@/composables/useImageChangeGuard'
import { useImagesStore } from '@/stores/images'
import { initFabricExtensions } from '@/utils/fabricExtensions'
import { getImageTypeFromTag } from '@/utils/imageUtils'
import { renderJsonData } from '@/utils/jsonRenderer'
import Editor, { type IEditor } from '@kuaitu/core'
import { fabric } from 'fabric'

const currentTag = ref('mainImg')
const showContentSection = ref(false)
const canvasEditor = new Editor() as IEditor
const { initCanvas, hideControls } = useCanvas()
const { initPlugins } = useEditorPlugins(canvasEditor)
const { guardImageChange, resetChangeTracking } = useImageChangeGuard()
const imagesStore = useImagesStore()

const isTranslate = ref(false) // SKU图是否翻译
const isOffTranslationList = computed(() => currentTag.value === 'sku' && !isTranslate.value)

// 计算当前选中的图片类型
const currentImageType = computed(() => getImageTypeFromTag(currentTag.value))

// 当前选中的图片
const currentImage = computed(() => imagesStore.currentActiveImage)
const selectedLayerId = ref(-1)
// 记录用户手动编辑过的图层ID
const editedLayerIds = ref<Set<number>>(new Set())

// 图层数据 - 只读
const textLayerData = computed<TextLayer[]>(() => {
  return imagesStore.currentActiveImage?.layers?.filter(
    (layer): layer is TextLayer =>
      layer.type === 'text' && (!!layer.ocrContent || editedLayerIds.value.has(layer.id)),
  ) ?? []
})

// 更新图层文本内容
function updateLayerContent(index: number, content: string) {
  if (!imagesStore.currentActiveImage?.layers)
    return

  const layer = textLayerData.value[index]
  if (!layer)
    return

  // 记录这个图层被编辑过
  editedLayerIds.value.add(layer.id)

  // 找到原始图层并更新
  const originalLayer = imagesStore.currentActiveImage.layers.find(l => l.id === layer.id)
  if (originalLayer && originalLayer.type === 'text') {
    originalLayer.content = content
  }
}

// 更新图层备注
function updateLayerRemark(index: number, remark: string) {
  if (!imagesStore.currentActiveImage?.layers)
    return

  const layer = textLayerData.value[index]
  if (!layer)
    return

  // 找到原始图层并更新
  const originalLayer = imagesStore.currentActiveImage.layers.find(l => l.id === layer.id)
  if (originalLayer && originalLayer.type === 'text') {
    originalLayer.remark = remark
  }
}

// AI 翻译弹窗索引与当前编辑文本
const activePopoverIndex = ref(-1)
const currentEditingIndex = ref(-1)
const currentEditingText = ref('')

// 打开 AI 弹窗（Popover）并高亮选中的图层
function openAIPopover(index: number, layerId: number) {
  activePopoverIndex.value = index
  currentEditingIndex.value = index
  // 选中对应图层高亮
  handleLayerSelect(layerId)
}

// 清空当前行翻译内容
function clearTranslation(index: number) {
  updateLayerContent(index, '')
}

function clearAllTranslations() {
  textLayerData.value.forEach((_, index) => {
    updateLayerContent(index, '')
  })
}

// 处理翻译结果
function handleTranslateResult(result: string) {
  if (currentEditingIndex.value !== -1) {
    textLayerData.value[currentEditingIndex.value].content = result
    activePopoverIndex.value = -1
  }
}

// 处理中文图层选中
function handleLayerSelect(layerId: number) {
  if (typeof layerId !== 'number' || layerId < 0) {
    return
  }

  selectedLayerId.value = layerId
  const canvas = canvasEditor.fabricCanvas
  if (!canvas) {
    return
  }

  canvas.getObjects().forEach((obj: any) => {
    if (!obj)
      return

    if (obj.id === layerId && !obj.data?.isForeignLayer) {
      // 高亮选中的图层
      if (obj.type === 'rectWithText') {
        obj.set({
          stroke: '#FF0000',
          strokeWidth: 4,
          fill: 'transparent',
        })
        // 确保图层可见
        obj.set('visible', true)
        if (obj.text && typeof obj.text.set === 'function') {
          obj.text.set('visible', true)
        }
      }
      canvas.setActiveObject(obj)
    }
    else {
      // 重置其他图层的样式
      if (obj.type === 'rectWithText') {
        obj.set({
          stroke: '#399E96',
          strokeWidth: !obj.data?.isForeignLayer ? 0 : 4,
          fill: 'transparent',
        })
      }
    }
  })

  // 重新渲染画布
  canvas.requestRenderAll()
}

// 保存图层数据
async function saveLayerData() {
  if (!currentImage.value)
    return

  try {
    const imageFile = await canvasEditor.saveImg(false)
    const { data: imageKey } = await uploadImage(imageFile)

    if (!imageKey || !canvasEditor.fabricCanvas)
      return

    const layerContent = imagesStore.currentActiveImage?.layers?.map(layer => ({
      ...layer,
      id: layer.id,
      content: layer.type === 'text' ? layer.content || '' : undefined,
      remark: layer.remark || '',
    }))
    const req: SaveImageLayersReq = {
      imageId: currentImage.value.id,
      file: imageKey,
      layerContent: JSON.stringify(layerContent),
    }

    await saveImageLayers(req)
    // 更新原始图层数据
    imagesStore.updateOriginalLayers(currentImage.value.id)
    resetChangeTracking()
  }
  catch (error) {
    console.error('Save layer data failed:', error)
    ElMessage.error('保存失败')
  }
}

// 清除非workspace对象并重新渲染画布
function clearAndRenderCanvas() {
  if (!canvasEditor.fabricCanvas)
    return

  // 选择性清除非workspace对象
  canvasEditor.fabricCanvas.getObjects().forEach((obj: any) => {
    if (canvasEditor.fabricCanvas && obj.id !== 'workspace') {
      canvasEditor.fabricCanvas.remove(obj)
    }
  })

  // 显示日文图层
  renderJsonData(
    canvasEditor,
    canvasEditor.fabricCanvas,
    {
      backgroundImg: imagesStore.currentActiveImage?.backgroundImg || '',
      layers: imagesStore.currentActiveImage?.layers || [],
    },
    false,
    true,
  )
}

// 切换上一张
async function prevImage() {
  const shouldSave = await guardImageChange(
    () => imagesStore.prevImage(currentImageType.value),
  )
  if (shouldSave) {
    clearAndRenderCanvas()
    await saveLayerData()
    imagesStore.prevImage(currentImageType.value)
  }
}

// 切换下一张
async function nextImage() {
  const shouldSave = await guardImageChange(
    () => imagesStore.nextImage(currentImageType.value),
  )
  if (shouldSave) {
    clearAndRenderCanvas()
    await saveLayerData()
    imagesStore.nextImage(currentImageType.value)
  }
}

// 初始化 Fabric 扩展
initFabricExtensions()

const isMounted = ref(false)
watch(
  () => [
    imagesStore.currentActiveImage,
    imagesStore.currentActiveImage?.layers,
    imagesStore.currentActiveImage?.status,
  ],
  ([newImage, newLayers, newStatus]: any, old?: any[]) => {
    if (!canvasEditor.fabricCanvas)
      return
    if (!newImage) {
      // 选择性清除非workspace对象
      const objects = canvasEditor.fabricCanvas.getObjects()
      objects.forEach((obj: any) => {
        if (obj.id !== 'workspace') {
          canvasEditor.fabricCanvas && canvasEditor.fabricCanvas.remove(obj)
        }
      })
      return
    }
    if (!isMounted.value)
      return

    // 检查是否需要重新渲染
    const [oldImage, oldLayers, oldStatus] = old || []
    const imageChanged = !oldImage || newImage.id !== oldImage.id
    const statusChanged = !oldStatus || newStatus !== oldStatus
    const layersChanged = !oldLayers || JSON.stringify(newLayers) !== JSON.stringify(oldLayers)

    if (!imageChanged && !statusChanged && !layersChanged)
      return

    // 关闭 AI 翻译弹窗
    activePopoverIndex.value = -1
    currentEditingIndex.value = -1
    currentEditingText.value = ''

    // 选择性清除非workspace对象
    const objects = canvasEditor.fabricCanvas.getObjects()
    objects.forEach((obj: any) => {
      if (obj.id !== 'workspace') {
        canvasEditor.fabricCanvas && canvasEditor.fabricCanvas.remove(obj)
      }
    })

    if (!newImage.backgroundImg)
      return

    // 如果图片状态是处理中，只显示背景图
    if (newImage.status !== TranslateStatus.SUCCESS) {
      renderJsonData(
        canvasEditor,
        canvasEditor.fabricCanvas,
        { backgroundImg: newImage.backgroundImg, layers: [] },
        true,
        false,
      )
      return
    }

    // 如果有图层数据，渲染完整内容
    renderJsonData(canvasEditor, canvasEditor.fabricCanvas, { backgroundImg: newImage.backgroundImg, layers: newLayers }, true, false)
    // 初始化变更追踪
    if (newImage.layers) {
      resetChangeTracking()
    }
  },
  { immediate: true },
)

onMounted(async () => {
  const canvas = initCanvas()
  canvasEditor.init(canvas)
  hideControls(canvas)
  initPlugins()

  isMounted.value = true

  await imagesStore.init()
})

onUnmounted(() => canvasEditor.destory())

provide('fabric', fabric)
provide('editor', canvasEditor)
provide('canvasEditor', canvasEditor)
provide('saveChanges', saveLayerData)

const currentTypeImages = computed(() => {
  return imagesStore.images.filter(img => img.type === currentImageType.value)
})
</script>

<template>
  <div class="home-view">
    <ImageTag v-model="currentTag" @content-selected="showContentSection = true" />
    <div class="workspace-container">
      <!-- 左侧缩略图列表 -->
      <div v-show="currentTag !== 'content'" class="thumbnail-container">
        <ThumbnailList :current-type="currentImageType" />
      </div>
      <!-- 文案部分 -->
      <ContentSection v-if="currentTag === 'content'" />
      <!-- 翻译及画布区域 -->
      <div v-else class="canvas_translation">
        <div class="control">
          <div class="control_btn">
            <el-button type="default" :disabled="!currentTypeImages.length" @click="prevImage">
              {{ $t('translate.prev_image') }}
            </el-button>
            <el-button type="default" :disabled="!currentTypeImages.length" @click="nextImage">
              {{ $t('translate.next_image') }}
            </el-button>
            <el-button type="default" @click="clearAllTranslations">
              {{ $t('translate.clear_all') }}
            </el-button>
          </div>
          <div v-show="currentTag === 'sku'" class="istranslate">
            <span>{{ $t('translate.is_translate') }}：</span>
            <el-radio-group v-model="isTranslate">
              <el-radio :value="true">
                {{ $t('translate.translate') }}
              </el-radio>
              <el-radio :value="false">
                {{ $t('translate.no_translate') }}
              </el-radio>
            </el-radio-group>
          </div>
        </div>
        <div v-loading="currentImage?.status !== TranslateStatus.SUCCESS && !!imagesStore.currentActiveImage" class="space">
          <div class="canvas-container">
            <div id="workspace" class="canvas-box">
              <div class="inside-shadow" />
              <canvas id="canvas" class="design-stage-grid" />
            </div>
          </div>
          <div v-show="!isOffTranslationList" class="translate-list">
            <div class="translation-panel">
              <div class="layers-container">
                <el-row :gutter="20">
                  <el-col
                    v-for="(layer, index) in textLayerData"
                    :key="layer.id"
                    :xs="24" :sm="24" :md="12" :lg="12"
                  >
                    <el-card class="layer-card" shadow="hover">
                      <template #header>
                        <div class="card-header">
                          <span>{{ $t('translate.translate_1') }} #{{ index + 1 }}</span>
                          <div class="header-actions">
                            <el-button
                              size="small"
                              type="primary"
                              @click.stop="() => openAIPopover(index, layer.id)"
                            >
                              AI
                            </el-button>
                            <el-button
                              size="small"
                              type="warning"
                              @click.stop="() => clearTranslation(index)"
                            >
                              {{ $t('translate.clear') }}
                            </el-button>
                          </div>
                        </div>
                      </template>

                      <div class="card-body">
                        <div class="input-row">
                          <ElPopover
                            :visible="activePopoverIndex === index"
                            placement="bottom-start"
                            hide-on-press-escape
                            :trigger-keys="[]"
                            :width="420"
                          >
                            <template #default>
                              <AITranslateDialog
                                :model-value="activePopoverIndex === index"
                                :initial-text="layer.ocrContent"
                                @translate-result="handleTranslateResult"
                                @close="activePopoverIndex = -1"
                              />
                            </template>
                            <template #reference>
                              <el-input
                                :model-value="layer.content"
                                type="textarea"
                                :placeholder="$t('page.translate.translation_result')"
                                :rows="3"
                                :maxlength="1000" show-word-limit
                                @update:model-value="val => updateLayerContent(index, val)"
                                @click.stop="() => handleLayerSelect(layer.id)"
                              />
                            </template>
                          </ElPopover>
                        </div>

                        <div class="input-row">
                          <el-input
                            :model-value="layer.remark"
                            type="textarea"
                            :placeholder="$t('translate.remark_placeholder')"
                            @update:model-value="val => updateLayerRemark(index, val)"
                          />
                        </div>
                      </div>
                    </el-card>
                  </el-col>
                </el-row>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
$border-color: #dcdfe6;
$hover-border-color: #409eff;
$active-border-color: #409eff;
$active-bg-color: #ecf5ff;
$background-color: #fff;
$shadow-color: rgba(0, 0, 0, 0.1);
$gray-color: #ededed;
$control-gap: 80px;
$layer-gap: 16px;
$padding-base: 16px;
$button-gap: 10px;
$canvas-height: 60vh;

.home-view {
  height: 100%;
  display: flex;
  flex-direction: column;

  .workspace-container {
    flex: 1;
    display: flex;
    overflow: hidden;

    .thumbnail-container {
      min-width: 14vw;
      width: 14vw;
      border-right: 1px solid #e0e0e0;
      overflow-y: hidden;
      padding: $padding-base;
    }

    .canvas_translation {
      flex: 1;
      display: flex;
      flex-direction: column;

      .control {
        display: flex;
        justify-content: center;
        gap: 80px;
        padding: 10px;

        .control_btn {
          display: flex;
          justify-content: center;
          gap: $control-gap;
        }

        .istranslate {
          display: flex;
          justify-content: center;
          align-items: center;

          > span {
            font-size: 14px;
            color: #6d6d6d;
          }
        }
      }

      .space {
        display: flex;
        flex: 1;
        min-height: 0;

        .canvas-container {
          width: 50%;
          display: flex;
          justify-content: center;
          align-items: center;
          min-width: 0;
          position: relative;
          padding: 0 1.2vw;
          padding-bottom: 16px;

          .canvas-box {
            position: relative;
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;

            .inside-shadow {
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              bottom: 0;
              pointer-events: none;
              background-color: $gray-color;
            }

            #canvas {
              position: absolute !important;
              top: 50% !important;
              left: 50% !important;
              transform: translate(-50%, -50%) !important;
            }
          }
        }

        .translate-list {
          width: 50%;
          height: 100%;
          background: $background-color;
          border-radius: 8px;
          overflow-y: auto;

          .translation-panel {
            padding: $padding-base;
            padding-top: 0;
            padding-bottom: 100px;
            overflow-y: auto;

            &-header {
              display: flex;
              margin-bottom: 10px;
              font-size: 14px;

              > span:first-child {
                width: 50%;
              }
            }

            .layers-container {
              .layer-card {
                margin-bottom: 16px;
                border-radius: 8px;

                :deep(.el-card__header) {
                  padding: 12px 16px !important;
                }

                .card-header {
                  display: flex;
                  justify-content: space-between;
                  align-items: center;

                  /* 标题文字 */
                  > span {
                    font-weight: 600;
                    font-size: 14px;
                    color: #333;
                  }

                  /* 标题右侧按钮区 */
                  .header-actions {
                    display: flex;
                    gap: 8px;
                  }
                }

                .card-body {
                  display: flex;
                  flex-direction: column;
                  gap: 16px;

                  .input-row {
                    .el-input {
                      width: 100%;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  .button-container {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
    gap: $button-gap;
    padding: 6px 0;
    background: $background-color;
    border-bottom: 1px solid #e7e7e7;
  }

  :deep(.el-button + .el-button) {
    margin-left: 0 !important;
  }
}
</style>

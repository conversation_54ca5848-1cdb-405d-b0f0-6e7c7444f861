<script setup lang="ts">
import { Close } from '@element-plus/icons-vue'

defineProps<{
  modelValue: boolean
  note: string
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void
}>()

function handleClose() {
  emit('update:modelValue', false)
}
</script>

<template>
  <div v-if="modelValue" class="design-note-panel">
    <div class="panel-header">
      <div class="panel-title">
        设计备注
      </div>
      <el-button class="close-button" circle @click="handleClose">
        <el-icon><Close /></el-icon>
      </el-button>
    </div>
    <div class="note-content">
      <div class="note-wrapper">
        <p v-if="!note" class="empty-note">
          暂无备注
        </p>
        <template v-else>
          {{ note }}
        </template>
      </div>
    </div>
  </div>
</template>

<style scoped>
.design-note-panel {
  height: 38%;
  display: flex;
  flex-direction: column;
  position: absolute;
  z-index: 10;
  top: 50%;
  right: 40px;
  transform: translateY(-50%);
  background-color: white;
  padding: 16px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  width: 280px;
  z-index: 100;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.panel-title {
  font-size: 16px;
  font-weight: 500;
}

.close-button {
  padding: 6px;
}

.note-content {
  background-color: #f8f9fa;
  border-radius: 4px;
  padding: 12px;
  max-height: 500px;
  overflow-y: auto;
  height: 100%;
}

.note-content::-webkit-scrollbar {
  width: 6px;
}

.note-content::-webkit-scrollbar-thumb {
  background-color: #dcdfe6;
  border-radius: 3px;
}

.note-content::-webkit-scrollbar-track {
  background-color: transparent;
}

.note-wrapper {
  white-space: pre-wrap;
  word-break: break-word;
  font-size: 14px;
  line-height: 1.6;
  color: #606266;
  letter-spacing: 0.3px;
}

.empty-note {
  text-align: center;
  color: #909399;
  margin: 0;
  font-style: italic;
}
</style>

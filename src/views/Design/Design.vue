<script setup lang="ts">
import { saveImageLayers, uploadImage } from '@/api/imageTranslationApi'
import ContentSection from '@/components/ContentSection/ContentSection.vue'
import DesignThumbnailList from '@/components/DesignThumbnailList/DesignThumbnailList.vue'
import DesignUploadDialog from '@/components/DesignUploadDialog/DesignUploadDialog.vue'
import ImageTag from '@/components/ImageTag/ImageTag.vue'
import MarkerList from '@/components/MarkerList/MarkerList.vue'
import { useDesignViewer } from '@/composables/useDesignViewer'
import { useThumbnailSelection } from '@/composables/useThumbnailSelection'
import { useDesignStore } from '@/stores/design'
import { useImagesStore } from '@/stores/images'
import { convertFabricObjectsToLayers } from '@/utils/fabricToLayer'
import { fabric } from 'fabric'
import { useI18n } from 'vue-i18n'

const currentTag = ref('mainImg')
const showContentSection = ref(false)
const showUploadDialog = ref(false)
const { t } = useI18n()

// 使用共享的设计稿查看器组合式函数
const {
  canvasEditor,
  currentImageType,
  currentMarkers,
  highlightMarker,
  highlightedMarkerId,
} = useDesignViewer(currentTag, { readonly: true })

// 使用缩略图选择组合式函数
const {
  handleThumbnailSelected,
  prevImage,
  nextImage,
} = useThumbnailSelection(currentImageType)

const imagesStore = useImagesStore()
const designStore = useDesignStore()

const activeObject = ref<any>(null)
canvasEditor.on('selectOne', (obj: any[]) => {
  activeObject.value = obj[0]
})
canvasEditor.on('selectCancel', () => {
  activeObject.value = null
})

// 监听当前图片类型变化
watch(
  () => currentImageType.value,
  (newType) => {
    if (imagesStore.transId) {
      designStore.fetchDesignInfo(newType, imagesStore.transId)
    }
  },
)

provide('fabric', fabric)
provide('canvasEditor', canvasEditor)

const isSaving = ref(false)
async function saveDesign() {
  try {
    if (canvasEditor.fabricCanvas) {
      const canvasObjs = canvasEditor.fabricCanvas.getObjects()
      // const layers = convertFabricObjectsToLayers(canvasObjs)
      const foreignLayers = convertFabricObjectsToLayers(canvasObjs.filter(obj => obj.data?.isForeignLayer))
      const chineseLayer = convertFabricObjectsToLayers(canvasObjs.filter(obj => !obj.data?.isForeignLayer))
      const layers = [...foreignLayers.map((fl) => {
        if (fl.type === 'text') {
          const matchingLayer = chineseLayer.find(cl => cl.id === fl.id)
          if (matchingLayer?.type === 'text') {
            return { ...fl, content: fl.content, ocrContent: matchingLayer.ocrContent }
          }
        }
        return fl
      })]
      isSaving.value = true
      const imageFile = await canvasEditor.saveImg(false)
      const { data: imageKey } = await uploadImage(imageFile)

      if (!imageKey)
        return
      // 保存图层内容和图片
      await saveImageLayers({
        imageId: imagesStore.currentActiveImage?.id,
        file: imageKey,
        layerContent: JSON.stringify(layers),
      })

      // 更新 store 中的图层数据
      if (imagesStore.currentActiveImage) {
        // 同时更新images
        const imageIndex = imagesStore.images.findIndex(img => img.id === imagesStore.currentActiveImage?.id)
        if (imageIndex !== -1) {
          imagesStore.images[imageIndex].layers?.splice(0, imagesStore.images[imageIndex].layers.length, ...layers)
        }

        // 同时更新原始图层数据，用于重置功能
        imagesStore.updateOriginalLayers(imagesStore.currentActiveImage?.id)
      }

      await designStore.fetchDesignInfo(currentImageType.value, imagesStore.transId)

      ElMessage.success('保存成功')
    }
  }
  catch (error) {
    console.error('Save design failed:', error)
    ElMessage.error('保存失败')
  }
  finally {
    isSaving.value = false
  }
}

provide('saveDesign', saveDesign)
</script>

<template>
  <div class="home-view">
    <ImageTag v-model="currentTag" @content-selected="showContentSection = true" />
    <div class="workspace-container">
      <!-- 左侧缩略图列表 -->
      <div v-show="currentTag !== 'content'" class="thumbnail-container">
        <DesignThumbnailList :current-type="currentImageType" @thumbnail-selected="handleThumbnailSelected" />
      </div>
      <!-- 文案部分 -->
      <ContentSection v-if="currentTag === 'content'" />
      <!-- 主要内容区域 -->
      <div v-else class="main-content">
        <div class="button-row">
          <div class="left-buttons">
            <el-button type="primary" @click="showUploadDialog = true">
              {{ t('upload.uploadDesign') }}
            </el-button>
          </div>
          <div class="right-buttons">
            <el-button type="default" @click="prevImage">
              {{ t('translate.prev_design') || '上一个设计稿' }}
            </el-button>
            <el-button type="default" @click="nextImage">
              {{ t('translate.next_design') || '下一个设计稿' }}
            </el-button>
          </div>
        </div>
        <div v-loading="designStore.getLoadingStateByType(currentImageType).value" class="design-area">
          <div class="content-area">
            <!-- 画布区域 -->
            <div class="canvas-section">
              <div id="workspace">
                <div class="canvas-box">
                  <div class="inside-shadow" />
                  <canvas id="canvas" />
                </div>
              </div>
            </div>
            <!-- 标记列表 -->
            <MarkerList
              :markers="currentMarkers"
              :editable="false"
              :highlighted-marker-id="highlightedMarkerId"
              @highlight-marker="highlightMarker"
            />
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 上传设计图片对话框 -->
  <DesignUploadDialog
    v-if="showUploadDialog"
    :visible="showUploadDialog"
    @update:visible="showUploadDialog = false"
    @upload-success="imagesStore.getAllImages(imagesStore.transId)"
  />
</template>

<style lang="scss" scoped>
.home-view {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.workspace-container {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.thumbnail-container {
  min-width: 14vw;
  width: 14vw;
  border-right: 1px solid #e0e0e0;
  overflow-y: hidden;
  padding: 16px;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.design-area {
  position: relative;
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;

  .control {
    display: flex;
    justify-content: center;
    gap: 80px;

    .control_btn {
      display: flex;
      justify-content: center;
      margin-bottom: 14px;
      gap: 20px;
      padding-top: 14px;
    }
  }
}

#workspace {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #f5f5f5;
  overflow: hidden;
}

.canvas-box {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

#canvas {
  position: absolute !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
}

.button-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;

  .left-buttons,
  .right-buttons {
    display: flex;
    gap: 8px;
  }

  .note-button {
    border-color: #ff0000;

    &:hover {
      border-color: #ff0000;
      background-color: #ffcccc;
      color: #ff0000;
    }
  }
}

:deep(.el-button + .el-button) {
  margin-left: 0 !important;
}

.iconfont {
  margin-right: 4px;
  font-size: 20px;
}

// 网格背景
.design-stage-grid {
  --offsetX: 0px;
  --offsetY: 0px;
  --size: 16px;
  --color: #dedcdc;
  background-image: linear-gradient(45deg, var(--color) 25%, transparent 0, transparent 75%, var(--color) 0),
    linear-gradient(45deg, var(--color) 25%, transparent 0, transparent 75%, var(--color) 0);
  background-position:
    var(--offsetX) var(--offsetY),
    calc(var(--size) + var(--offsetX)) calc(var(--size) + var(--offsetY));
  background-size: calc(var(--size) * 2) calc(var(--size) * 2);
}

.iconfont {
  margin-right: 4px;
  font-size: 20px;
}

.content-area {
  flex: 1;
  display: flex;
  height: 100%;
}

.canvas-section {
  flex: 1;
  display: flex;
  justify-content: center;
  height: 100%;
  width: 100%;
  overflow: hidden;
}

.original-image {
  flex: 1;
  min-width: 45%;
  background: #fff;
  padding: 10px;
  padding-top: 0;
  padding-bottom: 16px;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;

  img {
    display: block;
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
}

.design-info {
  margin-top: 20px;
  padding: 16px;
  background-color: #f9f9f9;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
}

.design-item {
  margin-bottom: 16px;

  &:last-child {
    margin-bottom: 0;
  }
}

.design-link,
.design-remark {
  margin-bottom: 8px;
  line-height: 1.5;
}

.label {
  font-weight: bold;
  margin-right: 8px;
}

.link {
  color: #409eff;
  text-decoration: none;

  &:hover {
    text-decoration: underline;
  }
}

.remark-content {
  white-space: pre-wrap;
}
</style>

<script setup lang="ts">
import type { RectWithText } from '@/utils/fabricExtensions'
import type { IEditor } from '@kuaitu/core'
import { Close } from '@element-plus/icons-vue'
import { fabric } from 'fabric'

const props = defineProps({
  activeObject: {
    type: Object as PropType<RectWithText | null>,
    required: true,
  },
})

const emits = defineEmits(['close', 'update'])

const canvasEditor = inject<IEditor>('canvasEditor')
const canvas = canvasEditor?.fabricCanvas

const fontSizes = ref([8, 9, 10, 11, 12, 14, 16, 18, 20, 22, 24, 26, 28, 36, 48, 72])

const activeText = computed(() => {
  if (!props.activeObject) {
    return null
  }
  // 使用可靠的类型检查方式
  if ('text' in props.activeObject) {
    return props.activeObject.text
  }
  return null
})

// 定义控制面板状态的类型
interface ControlPanelState {
  strokeWidth: number
  stroke: string
  fill: string
  data: { borderRadius: number }
}

// 定义可更新属性的类型
type UpdateableProperty = keyof ControlPanelState | 'data.borderRadius'

const localState = ref<ControlPanelState>({
  strokeWidth: 0,
  stroke: '#000000',
  fill: '#ffffff',
  data: { borderRadius: 0 },
})

watch(() => props.activeObject, (newObj) => {
  if (newObj) {
    localState.value = {
      strokeWidth: newObj.strokeWidth ?? 0,
      stroke: newObj.stroke ?? '#000000',
      fill: newObj.fill as string ?? '#ffffff',
      data: {
        borderRadius: newObj?.data?.borderRadius ?? 0,
      },
    }
  }
}, { immediate: true })

function updateProperty(property: UpdateableProperty, value: any) {
  if (property === 'data.borderRadius') {
    localState.value.data.borderRadius = Number(value)
  }
  else {
    if (property in localState.value) {
      (localState.value as any)[property] = value
    }
  }
  const updates: Partial<Record<UpdateableProperty | 'data.borderRadius', any>> = { [property]: value }

  // 发出更新事件
  emits('update', updates)
}

function updateText() {
  if (props.activeObject && !(props.activeObject instanceof fabric.ActiveSelection)) {
    const textUpdates = {
      text: props.activeObject.text.text,
      fontFamily: props.activeObject.text.fontFamily,
      fontSize: props.activeObject.text.fontSize,
    }
    emits('update', { text: textUpdates })
    canvas?.requestRenderAll()
  }
}

function updateFill() {
  if (props.activeObject && !(props.activeObject instanceof fabric.ActiveSelection)) {
    // 必需要触发字体大小变化才能设置颜色
    emits('update', {
      text: { text: props.activeObject.text.text, fill: props.activeObject.text.fill, fontSize: props.activeObject.text.fontSize! - 1 },
    })
    emits('update', {
      text: { text: props.activeObject.text.text, fill: props.activeObject.text.fill, fontSize: props.activeObject.text.fontSize! + 1 },
    })
    canvas?.requestRenderAll()
  }
}

function toggleFontWeight() {
  if (props.activeObject && !(props.activeObject instanceof fabric.ActiveSelection)) {
    const currentWeight = (props.activeObject as any).text.fontWeight
    let newWeight = currentWeight
    if (currentWeight === 'normal') {
      newWeight = 'bold'
    }
    else if (currentWeight === 'bold') {
      newWeight = 'normal'
    }
    else {
      newWeight = 'bold'
    }
    emits('update', { text: { fontWeight: newWeight } })
    canvas?.requestRenderAll()
  }
}

function setTextAlign(align: fabric.Text['textAlign']) {
  if (props.activeObject && !(props.activeObject instanceof fabric.ActiveSelection)) {
    emits('update', { text: { textAlign: align } })
    canvas?.requestRenderAll()
  }
}

function setVerticalAlign(align: 'top' | 'middle' | 'bottom') {
  if (props.activeObject && !(props.activeObject instanceof fabric.ActiveSelection)) {
    props.activeObject.setVerticalAlign(align)
    canvas?.requestRenderAll()
  }
}

function updateCharSpacing() {
  if (props.activeObject && !(props.activeObject instanceof fabric.ActiveSelection)) {
    emits('update', { text: { charSpacing: props.activeObject.text.charSpacing } })
    canvas?.requestRenderAll()
  }
}

function updateLineHeight() {
  if (props.activeObject && !(props.activeObject instanceof fabric.ActiveSelection)) {
    emits('update', { text: { lineHeight: props.activeObject.text.lineHeight } })
    canvas?.requestRenderAll()
  }
}

// 添加拖动相关的状态
const isDragging = ref(false)
const startX = ref(0)
const startY = ref(0)
const position = ref<'initial' | 'dragging'>('initial')
const panelPosition = ref({ left: '', top: '', transform: '' })

function closePanel() {
  // 重置拖动状态和位置
  isDragging.value = false
  position.value = 'initial'
  panelPosition.value = { left: '', top: '', transform: '' }
  emits('close')
}

function handleDragStart(e: MouseEvent) {
  const panel = document.querySelector('.control-panel') as HTMLElement
  if (!panel)
    return

  const rect = panel.getBoundingClientRect()
  const designArea = panel.closest('.design-area') as HTMLElement
  if (!designArea)
    return

  const designRect = designArea.getBoundingClientRect()

  // 计算相对于设计区域的位置
  const relativeLeft = rect.left - designRect.left
  const relativeTop = rect.top - designRect.top

  // 先设置初始位置，保持面板在当前位置不变
  panelPosition.value = {
    left: `${relativeLeft}px`,
    top: `${relativeTop}px`,
    transform: 'none',
  }

  // 设置拖拽起始点
  startX.value = e.clientX - relativeLeft
  startY.value = e.clientY - relativeTop

  // 延迟设置拖拽状态，确保位置已经应用
  requestAnimationFrame(() => {
    isDragging.value = true
    position.value = 'dragging'
  })

  document.addEventListener('mousemove', handleDragMove)
  document.addEventListener('mouseup', handleDragEnd)
}

function handleDragMove(e: MouseEvent) {
  if (!isDragging.value)
    return

  const panel = document.querySelector('.control-panel') as HTMLElement
  if (!panel)
    return

  const designArea = panel.closest('.design-area') as HTMLElement
  if (!designArea)
    return

  const designRect = designArea.getBoundingClientRect()
  const left = e.clientX - startX.value
  const top = e.clientY - startY.value

  // 确保不会拖出设计区域
  panelPosition.value = {
    left: `${Math.max(0, Math.min(designRect.width - panel.offsetWidth, left))}px`,
    top: `${Math.max(0, Math.min(designRect.height - panel.offsetHeight, top))}px`,
    transform: 'none',
  }
}

function handleDragEnd() {
  isDragging.value = false
  document.removeEventListener('mousemove', handleDragMove)
  document.removeEventListener('mouseup', handleDragEnd)
}

// 监听activeObject变化，当变为null时重置位置
watch(() => props.activeObject, (newVal) => {
  if (!newVal) {
    position.value = 'initial'
    panelPosition.value = { left: '', top: '', transform: '' }
  }
})
</script>

<template>
  <div
    v-if="activeObject" class="control-panel" :class="{ 'panel-dragging': position === 'dragging' }"
    :style="position === 'dragging' ? panelPosition : {}"
  >
    <div class="panel-header" @mousedown="handleDragStart">
      <div class="drag-handle">
        <div class="drag-line" />
        <div class="drag-line" />
      </div>
      <div class="panel-title">
        编辑图层
      </div>
      <el-button class="close-button" circle @click="closePanel">
        <el-icon>
          <Close />
        </el-icon>
      </el-button>
    </div>
    <div v-if="activeText" class="font-controls">
      <div class="property-row">
        <el-select v-model="activeText.fontSize" placeholder="字号" @change="updateText">
          <el-option v-for="size in fontSizes" :key="size" :label="size" :value="size" />
        </el-select>
        <el-color-picker v-model="activeText.fill as string" @change="updateFill" />
      </div>

      <div class="property-row">
        <el-input v-model.number="activeText.charSpacing" placeholder="字间距" @input="updateCharSpacing">
          <template #append>
            字间距
          </template>
        </el-input>
        <el-input v-model.number="activeText.lineHeight" placeholder="行高" @input="updateLineHeight">
          <template #append>
            行高
          </template>
        </el-input>
      </div>

      <el-button-group>
        <el-button :type="activeText.fontWeight === 'bold' ? 'primary' : ''" @click="toggleFontWeight()">
          B
        </el-button>
        <el-button :type="activeText.textAlign === 'left' ? 'primary' : ''" @click="setTextAlign('left')">
          <i class="iconfont icon-zuoduiqi" />
        </el-button>
        <el-button :type="activeText.textAlign === 'center' ? 'primary' : ''" @click="setTextAlign('center')">
          <i class="iconfont icon-juzhong" />
        </el-button>
        <el-button :type="activeText.textAlign === 'right' ? 'primary' : ''" @click="setTextAlign('right')">
          <i class="iconfont icon-youduiqi" />
        </el-button>
        <el-button @click="setVerticalAlign('middle')">
          <i class="iconfont icon-wenbenduiqi" />
        </el-button>
      </el-button-group>

      <div class="property-group">
        <div class="property-row">
          <el-input
            v-model.number="localState.strokeWidth" placeholder="边框粗度"
            @input="updateProperty('strokeWidth', $event)"
          >
            <template #append>
              边框粗度
            </template>
          </el-input>
          <el-color-picker v-model="localState.stroke" @change="updateProperty('stroke', $event)" />
        </div>

        <div class="property-row">
          <el-input
            v-model.number="localState.data.borderRadius" placeholder="圆角"
            @input="updateProperty('data.borderRadius', $event)"
          >
            <template #append>
              圆角
            </template>
          </el-input>
          <el-color-picker
            v-model="localState.fill" show-alpha color-format="hex" :predefine="[
              '#ff4500',
              '#ff8c00',
              '#ffd700',
              '#90ee90',
              '#00ced1',
              '#1e90ff',
              '#c71585',
              'rgba(255, 69, 0, 0.68)',
              'rgb(255, 120, 0)',
              'hsv(51, 100, 98)',
              'hsva(120, 40, 94, 0.5)',
              'hsl(181, 100%, 37%)',
              'hsla(209, 100%, 56%, 0.73)',
              '#c7158522',
            ]" @change="updateProperty('fill', $event)"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.control-panel {
  position: absolute;
  z-index: 10;
  top: 50%;
  right: 40px;
  transform: translateY(-50%);
  background-color: white;
  padding: 16px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  width: 280px;
  user-select: none;
  will-change: transform;
}

.panel-dragging {
  right: auto;
  top: auto;
  transform: none !important;
}

.panel-header {
  cursor: move;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;

  .drag-handle {
    cursor: move;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 8px 0;
    transition: opacity 0.2s;

    &:hover {
      opacity: 0.7;
    }

    .drag-line {
      width: 30px;
      height: 3px;
      background-color: #909399;
      border-radius: 2px;
      margin: 2px 0;

      &:first-child {
        margin-bottom: 2px;
      }
    }
  }
}

.panel-title {
  font-size: 16px;
  font-weight: 500;
}

.close-button {
  padding: 6px;
}

.font-controls {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

:deep(.el-select) {
  width: 100%;
}

:deep(.el-input) {
  width: 100%;
}

:deep(.el-button-group) {
  display: flex;
  width: 100%;
  margin: 8px 0;
}

:deep(.el-button-group .el-button) {
  flex: 1;
}

.property-group {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #ebeef5;
}

.property-row {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.property-row .el-input {
  flex: 1;
}

.action-buttons {
  display: flex;
  gap: 8px;
  margin-top: 16px;
}

.action-buttons .el-button {
  flex: 1;
}
</style>

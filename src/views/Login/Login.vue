<!-- src/views/Auth/Login.vue -->
<script lang="ts" setup>
import type { UserLoginReq } from '@/api/auth'
import type { ElForm } from 'element-plus'
import { generateCaptcha } from '@/api/auth'
import { useToolStore } from '@/stores/tool'
import { useUserStore } from '@/stores/user'
import { rsaEncrypt } from '@/utils/encrypt'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'

const { t } = useI18n()
// Form validation rules
const rules = computed(() => ({
  userName: [
    { required: true, message: t('page.login.account'), trigger: 'blur' },
  ],
  password: [
    { required: true, message: t('page.login.password'), trigger: 'blur' },
  ],
  captcha: [
    { required: true, message: t('page.login.captcha'), trigger: 'blur' },
  ],
}))

const loginFormRef = ref<InstanceType<typeof ElForm>>()
const captchaKey = ref('')
const captchaImage = ref('')
const isLoadingCaptcha = ref(false)

const formModel = reactive<UserLoginReq>({
  userName: '',
  password: '',
  captcha: '',
  captchaKey: '',
})

const isFetching = ref(false)

const userStore = useUserStore()
const toolStore = useToolStore()
const router = useRouter()

watch(
  () => router.currentRoute.value.path,
  (newPath: string) => {
    if (newPath === '/login')
      refreshCaptcha()
  },
)

async function refreshCaptcha() {
  try {
    isLoadingCaptcha.value = true
    const res = await generateCaptcha()
    if (res.success) {
      captchaKey.value = res.data.key
      captchaImage.value = res.data.textImage
      formModel.captchaKey = res.data.key
    }
  }
  finally {
    isLoadingCaptcha.value = false
  }
}

async function login() {
  if (!loginFormRef.value)
    return

  try {
    const valid = await loginFormRef.value.validate()
    if (valid) {
      isFetching.value = true
      const loginData = {
        ...formModel,
        password: rsaEncrypt(formModel.password),
      }
      const success = await userStore.loginAction(loginData)
      if (success) {
        // 尝试从 search 和 hash 中获取 redirect
        const searchRedirect = new URLSearchParams(window.location.search).get('redirect')
        const hashQuery = window.location.hash.split('?')[1]
        const hashRedirect = hashQuery ? new URLSearchParams(hashQuery).get('redirect') : null
        const redirect = searchRedirect || hashRedirect

        if (redirect) {
          window.location.href = decodeURIComponent(redirect)
        }
        else if (toolStore.selectedTool === 'freight') {
          window.location.href = '/logistics-tool/#/freight'
        }
        else if (toolStore.selectedTool === 'warehouse') {
          window.location.href = '/logistics-tool/#/freight-list'
        }
        else {
          router.push('/upload')
        }
      }
      else {
        refreshCaptcha()
      }
    }
  }
  finally {
    isFetching.value = false
  }
}

onMounted(() => {
  refreshCaptcha()
})
</script>

<template>
  <div class="login">
    <div class="login-container">
      <h2 class="title">
        {{ $t('page.login.title') }}
      </h2>
      <el-form
        ref="loginFormRef" :model="formModel" :rules="rules" :validate-on-rule-change="false" class="login-form"
        @keyup.enter="login"
      >
        <el-form-item prop="userName">
          <el-input v-model="formModel.userName" size="large" :placeholder="t('page.login.account_placeholder')" />
        </el-form-item>

        <el-form-item prop="password">
          <el-input
            v-model="formModel.password" type="password" size="large"
            :placeholder="t('page.login.password_placeholder')" show-password
          />
        </el-form-item>

        <el-form-item prop="captcha">
          <div class="captcha-container">
            <el-input v-model="formModel.captcha" size="large" :placeholder="t('page.login.captcha_placeholder')" />
            <div class="captcha-img" @click="refreshCaptcha">
              <div v-if="isLoadingCaptcha" class="captcha-loading">
                <div class="loading-shimmer" />
              </div>
              <img v-else :src="captchaImage" alt="验证码">
            </div>
          </div>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" :loading="isFetching" class="login-button" @click="login">
            {{ t('page.login.title') }}
          </el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.login {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  background: #fcfcfc;

  .login-container {
    padding: 30px;
    border-radius: 8px;
    text-align: center;
    width: 400px;
    transform: translateY(-100px);

    .title {
      font-size: 36px;
      margin-bottom: 26px;
      letter-spacing: 30px;
      text-indent: 30px;
      color: #333;
    }

    .login-form {
      text-align: left;

      :deep(.el-form-item) {
        margin-bottom: 22px;

        &:last-child {
          margin-bottom: 0;
        }

        .el-form-item__error {
          position: absolute;
          top: 100%;
          left: 0;
        }
      }

      .captcha-container {
        width: 100%;
        display: flex;
        gap: 10px;

        .el-input {
          flex: 1;
        }

        .captcha-img {
          width: 120px;
          height: 40px;
          cursor: pointer;
          overflow: hidden;
          position: relative;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: opacity 0.3s ease;
          }

          .captcha-loading {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #f5f7fa;

            .loading-shimmer {
              width: 100%;
              height: 100%;
              background: linear-gradient(
                90deg,
                rgba(255, 255, 255, 0) 0%,
                rgba(255, 255, 255, 0.3) 50%,
                rgba(255, 255, 255, 0) 100%
              );
              animation: shimmer 1.5s infinite;
            }
          }

          @keyframes shimmer {
            from {
              transform: translateX(-100%);
            }

            to {
              transform: translateX(100%);
            }
          }
        }
      }

      .login-button {
        width: 100%;
        height: 50px;
        margin-top: 20px;
        font-size: 20px;
        color: #fff;
        letter-spacing: 20px;
        text-indent: 20px;
      }
    }
  }
}
</style>

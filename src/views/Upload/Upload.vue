<script setup lang="ts">
import { listTranslations, startTranslation } from '@/api/imageTranslationApi'
import { ImageType, type TranslationVO } from '@/api/types'
import { useImagesStore } from '@/stores/images'
import { useSpuStore } from '@/stores/spu'
import { useUserStore } from '@/stores/user'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import SpuTable from './SpuTable.vue'
import ZipUpload from './ZipUpload.vue'
import ZipUploadDialog from './ZipUploadDialog.vue'

const imagesStore = useImagesStore()
const userStore = useUserStore()
const spuStore = useSpuStore()
const { t } = useI18n()
const formRef = ref()
const inputRef = ref()
const searching = ref(false)
const hasSearched = ref(false)
const startWorkLoading = ref(false)
const canSearch = ref(true)

const form = reactive({
  spu: '',
})
const rules = {
  spu: [{ required: true, message: t('upload.spuRequired'), trigger: 'change' }],
}
const isShowZipUpload = ref(false)
const isShowZipUploadDialog = ref(false)

const tableData = ref<TranslationVO[]>([])
const currentPage = ref(1)
const pageSize = ref(5)
const totalItems = ref(0)

async function handleSearch() {
  if (!canSearch.value || searching.value)
    return

  try {
    await formRef.value.validate()
    searching.value = true
    canSearch.value = false
    form.spu = form.spu.trim()
    const { data, success } = await listTranslations(form.spu, currentPage.value, pageSize.value)

    if (success) {
      isShowZipUpload.value = false
      tableData.value = data.records.map(item => ({
        spu: item.spu,
        creator: item.creator,
        createTime: item.createTime,
        updateTime: item.updateTime,
        transId: item.transId,
      }))
      totalItems.value = data.total
      hasSearched.value = true
    }
  }
  catch {
    return
  }
  finally {
    searching.value = false
    // 设置一个短暂的延迟后才允许下一次请求
    setTimeout(() => {
      canSearch.value = true
    }, 300)
  }
}

const router = useRouter()
async function handleStartTranslation(transId: string, spu: string) {
  const { success } = await startTranslation(transId)
  if (success) {
    imagesStore.transId = transId
    imagesStore.getAllImages(transId)
    imagesStore.startPolling(transId, ImageType.MAIN)
    const path = userStore.getDefaultPage()
    router.push({
      path,
      query: { transId: transId.toString(), spu },
    })
  }
}

// 上传成功处理
function handleUploadSuccess(transId: string, spu: string) {
  isShowZipUpload.value = false
  handleStartTranslation(transId, spu)
}

// 开始工作
async function handleStartWork(row: TranslationVO) {
  try {
    startWorkLoading.value = true
    handleStartTranslation(row.transId, row.spu)
    spuStore.spu = row.spu
  }
  finally {
    startWorkLoading.value = false
  }
}

function handleUploadSupply(row: TranslationVO) {
  isShowZipUpload.value = true
  form.spu = row.spu
}

function handleUpload() {
  isShowZipUploadDialog.value = true
}

function handlePageChange(page: number) {
  currentPage.value = page
  handleSearch()
}

onMounted(() => {
  inputRef.value?.focus()
})
</script>

<template>
  <div class="upload-container">
    <el-form ref="formRef" :model="form" :rules="rules" @submit.prevent>
      <div class="search-row">
        <el-form-item prop="spu" :label="`${t('upload.spu')}：`">
          <el-input
            ref="inputRef" v-model="form.spu" :placeholder="t('upload.spuPlaceholder')" maxlength="100"
            @keyup.enter.prevent="handleSearch"
          />
        </el-form-item>

        <el-button type="primary" :loading="searching" @click.prevent="handleSearch">
          {{ t('upload.search') }}
        </el-button>
        <el-button @click.prevent="handleUpload">
          {{ t('upload.upload') }}
        </el-button>
      </div>
    </el-form>

    <ZipUploadDialog
      v-if="isShowZipUploadDialog"
      v-model:visible="isShowZipUploadDialog"
      @upload-success="handleUploadSuccess"
    />

    <!-- 根据搜索结果显示不同的内容 -->
    <template v-if="hasSearched">
      <template v-if="tableData.length && !isShowZipUpload">
        <SpuTable
          :loading="searching" :table-data="tableData" :start-work-loading="startWorkLoading"
          @start-work="handleStartWork" @upload-supply="handleUploadSupply"
        />
        <el-pagination
          v-model:current-page="currentPage"
          :page-size="pageSize"
          :total="totalItems"
          class="pagination"
          @current-change="handlePageChange"
        />
      </template>
      <template v-else>
        <ZipUpload :spu="form.spu" @upload-success="(transId) => handleUploadSuccess(transId, form.spu)" />
      </template>
    </template>
  </div>
</template>

<style scoped lang="scss">
.upload-container {
  padding: 20px;
  max-width: 800px;
  margin: 100px auto 0 auto;
}

.upload-notes {
  margin: 20px 0;
  padding: 15px;
  background: #f5f7fa;
  border-radius: 4px;

  ol {
    margin: 10px 0 0 20px;

    li {
      line-height: 1.8;
    }
  }
}

.upload-area {
  width: 100%;
}

.search-row {
  width: 56%;
  margin: 0 auto;
  display: flex;
  align-items: center;
  gap: 16px;

  .el-form-item {
    flex: 1;
    margin-bottom: 0;
  }
}

.pagination {
  margin-top: 10px;
}
</style>

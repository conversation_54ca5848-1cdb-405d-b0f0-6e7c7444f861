<script setup lang="ts">
import type { UploadInstance, UploadProps, UploadRawFile } from 'element-plus'
import { uploadMaterial } from '@/api/imageTranslationApi'
import { UploadFilled } from '@element-plus/icons-vue'
import { genFileId } from 'element-plus'
import { useI18n } from 'vue-i18n'

defineProps<{
  visible: boolean
}>()

const emit = defineEmits<{
  (e: 'uploadSuccess', transId: string, spu: string): void
  (e: 'update:visible', value: boolean): void
}>()

const { t, locale } = useI18n()

const uploadRef = ref<UploadInstance>()
const currentFile = ref<UploadRawFile>()
const uploading = ref(false)
const formRef = ref()

// 根据当前语言设置标签宽度
const labelWidth = computed<string>(() => {
  return locale.value === 'ja' ? '106px' : '80px'
})

// 源语言选项
const sourceLanguageOptions = [
  { value: 'zh', label: t('upload.languages.zh') },
  { value: 'en', label: t('upload.languages.en') },
  { value: 'ja', label: t('upload.languages.ja') },
  { value: 'ko', label: t('upload.languages.ko') },
]

// 目标语言选项
const targetLanguageOptions = [
  { value: 'ja', label: t('upload.targetLanguageOptions.ja') },
  { value: 'ko', label: t('upload.targetLanguageOptions.ko') },
]

const form = reactive({
  spu: '',
  sourceLang: 'zh', // 默认选中文
  targetLang: 'ja', // 默认选中日语
})

const rules = {
  spu: [{ required: true, message: t('upload.spuRequired'), trigger: 'change' }],
  sourceLang: [{ required: true, message: t('upload.sourceLanguageRequired'), trigger: 'change' }],
  targetLang: [{ required: true, message: t('upload.targetLanguageRequired'), trigger: 'change' }],
}

const beforeUpload: UploadProps['beforeUpload'] = (file) => {
  if (!file.name.endsWith('.zip')) {
    ElMessage.error(t('upload.uploadZipOnly'))
    return false
  }

  const maxSize = 100 * 1024 * 1024
  if (file.size > maxSize) {
    ElMessage.error(t('upload.fileSizeExceed'))
    return false
  }

  return true
}

const handleChange: UploadProps['onChange'] = (uploadFile) => {
  currentFile.value = uploadFile.raw
}

const handleRemove: UploadProps['onRemove'] = () => {
  currentFile.value = undefined
}

const handleExceed: UploadProps['onExceed'] = (files) => {
  uploadRef.value!.clearFiles()
  const file = files[0] as UploadRawFile
  file.uid = genFileId()
  uploadRef.value!.handleStart(file)
}

async function submitUpload() {
  if (!formRef.value)
    return

  try {
    await formRef.value.validate()

    if (!currentFile.value) {
      ElMessage.warning(t('upload.pleaseSelectFile'))
      return
    }

    uploading.value = true
    const response = await uploadMaterial(
      currentFile.value,
      form.spu,
      form.sourceLang,
      form.targetLang,
    )
    if (response.data?.transId) {
      emit('uploadSuccess', response.data.transId, form.spu)
    }
  }
  finally {
    uploading.value = false
  }
}
</script>

<template>
  <el-dialog
    :model-value="visible"
    :title="t('upload.upload')"
    width="550px"
    destroy-on-close
    @update:model-value="(val: boolean) => emit('update:visible', val)"
  >
    <el-form ref="formRef" :model="form" :rules="rules" label-position="left" :label-width="labelWidth">
      <el-form-item :label="t('upload.spu')" prop="spu">
        <el-input v-model="form.spu" :placeholder="t('upload.spuPlaceholder')" maxlength="100" />
      </el-form-item>

      <el-form-item :label="t('upload.sourceLanguage')" prop="sourceLang">
        <el-select v-model="form.sourceLang" class="source-lang-select">
          <el-option
            v-for="option in sourceLanguageOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
      </el-form-item>

      <!-- 目标语言 -->
      <el-form-item :label="t('upload.targetLanguage')" prop="targetLang">
        <el-select v-model="form.targetLang" class="source-lang-select">
          <el-option
            v-for="option in targetLanguageOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
      </el-form-item>

      <div class="zip-upload-container">
        <div class="zip-upload">
          <div class="upload-demo">
            <el-upload
              ref="uploadRef"
              class="upload-demo"
              :auto-upload="false"
              :on-change="handleChange"
              :on-remove="handleRemove"
              :before-upload="beforeUpload"
              accept=".zip"
              :multiple="false"
              :limit="1"
              :on-exceed="handleExceed"
              drag
            >
              <el-icon class="el-icon--upload">
                <UploadFilled />
              </el-icon>
              <div class="el-upload__text">
                {{ t('upload.dragText') }} <em>{{ t('upload.clickText') }}</em>
              </div>
            </el-upload>
            <div class="tips">
              <h4>{{ t('upload.uploadNotes.title') }}</h4>
              <ol>
                <li>{{ t('upload.uploadNotes.format') }}</li>
                <li>{{ t('upload.uploadNotes.zipFormat') }}</li>
                <li>{{ t('upload.uploadNotes.zipSize') }}</li>
                <li>{{ t('upload.uploadNotes.folderStructure') }}</li>
              </ol>
            </div>
          </div>
        </div>
      </div>
    </el-form>

    <template #footer>
      <el-button @click="emit('update:visible', false)">
        {{ t('dialog.ai_translate.cancel') }}
      </el-button>
      <el-button type="primary" :loading="uploading" @click="submitUpload">
        {{ t('upload.startWork') }}
      </el-button>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
.zip-upload-container {
  margin-top: 20px;
  width: 100%;
  height: 100%;
}

.zip-upload {
  height: 100%;
  display: flex;
  justify-content: center;
}

.upload-icon {
  margin-right: 20px;
  font-size: 28px;
  color: #51994f;
}

.upload-demo {
  display: flex;
  flex-direction: column;
}

.tips {
  h4 {
    margin: 5px;
  }

  ol {
    margin: 3px;
    padding-left: 20px;

    li {
      line-height: 1.8;
    }
  }
}

.do {
  margin-left: 20px;
  margin-top: 70px;
}

:deep(.el-upload) {
  width: 100%;
}

:deep(.el-upload-list) {
  width: 328px;
}

.source-lang-select {
  width: 40%;
}
</style>

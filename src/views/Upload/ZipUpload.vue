<script setup lang="ts">
import type { UploadInstance, UploadProps, UploadRawFile } from 'element-plus'
import { uploadMaterial } from '@/api/imageTranslationApi'
import { UploadFilled } from '@element-plus/icons-vue'
import { genFileId } from 'element-plus'
import { useI18n } from 'vue-i18n'

const props = defineProps<{
  spu: string
}>()

const emit = defineEmits<{
  (e: 'uploadSuccess', transId: string): void
}>()

const { t, locale } = useI18n()

const uploadRef = ref<UploadInstance>()
const currentFile = ref<UploadRawFile>()
const uploading = ref(false)
const formRef = ref()

// 根据当前语言设置标签宽度
const labelWidth = computed<string>(() => {
  return locale.value === 'ja' ? '106px' : '80px'
})

// 源语言选项
const sourceLanguageOptions = [
  { value: 'zh', label: t('upload.languages.zh') },
  { value: 'en', label: t('upload.languages.en') },
  { value: 'ja', label: t('upload.languages.ja') },
  { value: 'ko', label: t('upload.languages.ko') },
]

// 目标语言选项
const targetLanguageOptions = [
  { value: 'ja', label: t('upload.targetLanguageOptions.ja') },
  { value: 'ko', label: t('upload.targetLanguageOptions.ko') },
]

const form = reactive({
  sourceLang: 'zh', // 默认选中文
  targetLang: 'ja', // 默认选中日语
})

const rules = {
  sourceLang: [{ required: true, message: t('upload.sourceLanguageRequired'), trigger: 'change' }],
  targetLang: [{ required: true, message: t('upload.targetLanguageRequired'), trigger: 'change' }],
}

const beforeUpload: UploadProps['beforeUpload'] = (file) => {
  if (!file.name.endsWith('.zip')) {
    ElMessage.error(t('upload.uploadZipOnly'))
    return false
  }

  const maxSize = 100 * 1024 * 1024
  if (file.size > maxSize) {
    ElMessage.error(t('upload.fileSizeExceed'))
    return false
  }

  return true
}

const handleChange: UploadProps['onChange'] = (uploadFile) => {
  currentFile.value = uploadFile.raw
}

const handleRemove: UploadProps['onRemove'] = () => {
  currentFile.value = undefined
}

const handleExceed: UploadProps['onExceed'] = (files) => {
  uploadRef.value!.clearFiles()
  const file = files[0] as UploadRawFile
  file.uid = genFileId()
  uploadRef.value!.handleStart(file)
}

async function submitUpload() {
  if (!formRef.value)
    return

  try {
    await formRef.value.validate()

    if (!currentFile.value) {
      ElMessage.warning(t('upload.pleaseSelectFile'))
      return
    }

    uploading.value = true
    const response = await uploadMaterial(currentFile.value, props.spu, form.sourceLang, form.targetLang)
    if (response.data?.transId) {
      emit('uploadSuccess', response.data.transId)
    }
  }
  finally {
    uploading.value = false
  }
}
</script>

<template>
  <div class="zip-upload-container">
    <div class="zip-upload">
      <el-form ref="formRef" :model="form" :rules="rules" label-position="left" :label-width="labelWidth">
        <el-form-item :label="`${t('upload.sourceLanguage')}：`" prop="sourceLang" class="source-lang-label">
          <el-select v-model="form.sourceLang" class="source-lang-select">
            <el-option
              v-for="option in sourceLanguageOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>

        <!-- 目标语言 -->
        <el-form-item :label="`${t('upload.targetLanguage')}：`" prop="targetLang" class="source-lang-label">
          <el-select v-model="form.targetLang" class="source-lang-select">
            <el-option
              v-for="option in targetLanguageOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>

        <div class="upload-demo">
          <el-upload
            ref="uploadRef"
            class="upload-demo"
            :auto-upload="false"
            :on-change="handleChange"
            :on-remove="handleRemove"
            :before-upload="beforeUpload"
            accept=".zip"
            :multiple="false"
            :limit="1"
            :on-exceed="handleExceed"
            drag
          >
            <el-icon class="el-icon--upload">
              <UploadFilled />
            </el-icon>
            <div class="el-upload__text">
              {{ t('upload.dragText') }} <em>{{ t('upload.clickText') }}</em>
            </div>
          </el-upload>
          <div class="tips">
            <h4>{{ t('upload.uploadNotes.title') }}</h4>
            <ol>
              <li>{{ t('upload.uploadNotes.format') }}</li>
              <li>{{ t('upload.uploadNotes.zipFormat') }}</li>
              <li>{{ t('upload.uploadNotes.zipSize') }}</li>
              <li>{{ t('upload.uploadNotes.folderStructure') }}</li>
            </ol>
          </div>
        </div>
      </el-form>

      <el-button type="primary" :loading="uploading" class="do" @click="submitUpload">
        {{ t('upload.startWork') }}
      </el-button>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.zip-upload-container {
  width: 100%;
  height: 100%;
  padding: 20px;
}

.zip-upload {
  height: 100%;
  margin-bottom: 20px;

  display: flex;
  justify-content: center;
}

.upload-icon {
  margin-right: 20px;
  font-size: 28px;
  color: #51994f;
}

.upload-demo {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.tips {
  width: 100%;
  max-width: 348px;
  padding: 15px;
  border-radius: 4px;

  h4 {
    margin-bottom: 10px;
    padding-left: 7px;
  }

  ol {
    padding-left: 20px;

    li {
      line-height: 1.8;
    }
  }
}

.do {
  margin-left: 20px;
  margin-top: 140px;
}

:deep(.el-upload) {
  width: 100%;
}

:deep(.el-upload-list) {
  width: 328px;
}

.source-lang-select {
  width: 120px;
}

:deep(.source-lang-label) {
  label {
    margin-left: 2px;
  }
  .el-form-item__content {
    margin-left: -2px;
  }
}
</style>

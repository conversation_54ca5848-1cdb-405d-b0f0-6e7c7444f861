<script setup lang="ts">
import type { TranslationVO } from '@/api/types'
import dayjs from 'dayjs'
import { useI18n } from 'vue-i18n'

// 定义props
defineProps<{
  loading: boolean
  tableData: TranslationVO[]
  startWorkLoading: boolean
}>()

// 定义emit
const emit = defineEmits<{
  startWork: [row: TranslationVO]
  uploadSupply: [row: TranslationVO]
}>()

const { t } = useI18n()

// 开始工作
function handleStartWork(row: TranslationVO) {
  emit('startWork', row)
}

// 补充上传
function handleUploadSupply(row: TranslationVO) {
  emit('uploadSupply', row)
}
</script>

<template>
  <el-table v-loading="loading" :data="tableData" style="margin-top: 20px" border>
    <el-table-column prop="spu" :label="t('upload.spu')" width="180" align="center" />
    <el-table-column prop="creator" :label="t('upload.operator')" width="180" align="center" />
    <el-table-column :label="t('upload.operateTime')" width="180" align="center">
      <template #default="{ row }">
        {{ dayjs(row.createTime).format('YYYY-MM-DD HH:mm:ss') }}
      </template>
    </el-table-column>
    <el-table-column :label="t('upload.operation')" min-width="200" align="center">
      <template #default="{ row }">
        <el-button type="primary" link :disabled="startWorkLoading" @click="handleStartWork(row)">
          {{ t('upload.startWork') }}
        </el-button>
        <el-button type="primary" link @click="handleUploadSupply(row)">
          {{ t('upload.uploadSupply') }}
        </el-button>
      </template>
    </el-table-column>
  </el-table>
</template>

<style scoped lang="scss">
.el-table {
  width: 100%;
}
</style>

interface ApiResponse {
  RequestId: string
  Data: Data
  Code: string
}

interface Data {
  InPaintingUrl: string
  TemplateJson: string
  FinalImageUrl: string
}

interface Template {
  backgroundColor: string
  top: number
  left: number
  width: number
  height: number
  scale: number
  opacity: number
  type: 'stage'
  children: Child[]
  token: string
}

type Child = ImageChild | TextChild

interface ImageChild {
  type: 'image'
  id: number
  label: string
  backgroundImg?: string
  top: number
  left: number
  width: number
  height: number
  zIndex: number
  src: string
}

interface TextChild {
  type: 'text'
  id: number
  label: string
  backgroundColor: string
  color: string
  textAlign: 'left' | 'center' | 'right'
  letterSpacing: number
  fontFamily: string
  fontSize: number
  lineHeight: number
  content: string
  ocrContent?: string
  pairId?: number
  numlines?: number
  contents?: string[]
  direction?: number
  top: number
  left: number
  width: number
  height: number
  zIndex: number
}

export type { ApiResponse, Child, Data, ImageChild, Template, TextChild }

import type { RectWithTextOptions } from '@/utils/fabricExtensions'
import type { IEditor } from '@kuaitu/core'
import { fabric } from 'fabric'

export interface TextBoxOptions {
  content?: string
  fontSize?: number
  lineHeight?: number
  color?: string
  backgroundColor?: string
  width?: number
  height?: number
  left?: number
  top?: number
  numlines?: number
  isForeignLayer?: boolean
  showTextBoxBorder?: boolean
  textboxBorderColor?: string
  textboxBorderWidth?: number
  textboxBorderRadius?: number
  visible?: boolean
  ignoreGuideline?: boolean
}

const DEFAULT_OPTIONS = {
  fontFamily: 'itcavantgardestd-bold, MiSans',
  fontSize: 16,
  borderColor: '#56B5AD',
  borderDashArray: [10, 5],
  padding: 2,
  hasControls: true,
  splitByGrapheme: true,
  showTextBoxBorder: true,
  textboxBorderColor: 'transparent',
  textboxBorderWidth: 0,
  textboxBorderRadius: 0,
  lineHeight: 1.5,
  visible: true,
}

export function useTextBox() {
  const createTextBox = (editor: IEditor, options: TextBoxOptions) => {
    const {
      content = '',
      fontSize = DEFAULT_OPTIONS.fontSize,
      lineHeight: rawLineHeight,
      color,
      backgroundColor,
      width = 0,
      height = 0,
      left = 0,
      top = 0,
      numlines = 1,
      isForeignLayer = false,
      showTextBoxBorder = DEFAULT_OPTIONS.showTextBoxBorder,
      textboxBorderColor = DEFAULT_OPTIONS.textboxBorderColor,
      textboxBorderWidth = DEFAULT_OPTIONS.textboxBorderWidth,
      textboxBorderRadius = DEFAULT_OPTIONS.textboxBorderRadius,
      visible = DEFAULT_OPTIONS.visible,
      ignoreGuideline = false,
    } = options

    const lineHeight = rawLineHeight ? rawLineHeight / fontSize : DEFAULT_OPTIONS.lineHeight

    const text = new fabric.Textbox(content, {
      ...DEFAULT_OPTIONS,
      fill: color,
      fontSize,
      backgroundColor,
      lineHeight,
      showTextBoxBorder,
      textboxBorderColor,
      textboxBorderWidth,
      textboxBorderRadius,
      visible,
      ignoreGuideline: ignoreGuideline || !visible, // 当文本隐藏时自动忽略辅助线
    } as fabric.ITextboxOptions & {
      showTextBoxBorder: boolean
      textboxBorderColor: string
      textboxBorderWidth: number
      textboxBorderRadius: number
    })

    const textHeight = fontSize * numlines
    const centerTop = top + (height - textHeight) / 2

    // 设置数据标记
    if (isForeignLayer) {
      text.data = { isForeignLayer: true }
    }

    editor.addBaseType(text)

    text.set({
      width,
      height,
      left,
      top: centerTop,
    })

    // 禁用顶部和底部控制点
    // text.setControlVisible('mt', false)
    // text.setControlVisible('mb', false)

    return text
  }

  const createCenteredTextBox = (editor: IEditor, options: Partial<TextBoxOptions> = {}) => {
    const canvas = editor.fabricCanvas
    const workspace = editor.getWorkspase()
    if (!canvas || !workspace)
      return

    const width = options.width || 128
    const height = options.height || 100

    // 使用workspace的尺寸来计算中心位置
    const workspaceWidth = workspace.width || canvas.getWidth()
    const workspaceHeight = workspace.height || canvas.getHeight()

    const canvasCenter = {
      left: (workspaceWidth - width) / 2,
      top: (workspaceHeight - height) / 2,
    }

    return createTextBox(editor, {
      content: '双击编辑文本',
      width,
      height,
      ...canvasCenter,
      ...options,
    })
  }

  const createRectWithText = (editor: IEditor, options: {
    rectOptions?: Partial<fabric.IRectOptions> & { id?: string | number }
    textOptions?: Partial<fabric.ITextboxOptions> & { id?: string | number }
    text?: string
  } = {}) => {
    const { rectOptions = {}, textOptions = {}, text = '' } = options

    const defaultRectOptions = {
      left: 10,
      top: 10,
      width: 200,
      height: 75,
      fill: 'rgba(30, 30, 30, 0.3)',
      ignoreGuideline: rectOptions.visible === false, // 当矩形不可见时忽略辅助线
      type: 'rectWithText',
      ...rectOptions,
    }

    const defaultTextOptions = {
      left: defaultRectOptions.left + 25,
      top: defaultRectOptions.top + 20,
      width: defaultRectOptions.width - 50,
      fill: 'white',
      fontSize: 30,
      ignoreGuideline: textOptions.visible === false, // 当文本不可见时忽略辅助线
      ...textOptions,
    }

    const rectWithText = new (fabric as any).RectWithText(
      defaultRectOptions,
      defaultTextOptions,
      text,
    ) as RectWithTextOptions

    editor.fabricCanvas?.add(rectWithText as any)
    editor.fabricCanvas?.setActiveObject(rectWithText as any)
    return rectWithText
  }

  return {
    createTextBox,
    createCenteredTextBox,
    createRectWithText,
  }
}

import type Editor from '@kuaitu/core'
import { EventType } from '@kuaitu/core'
import { get } from 'lodash-es'

const { SelectEvent, SelectMode } = EventType

export interface Selector {
  mSelectMode: (typeof SelectMode)[keyof typeof SelectMode]
  mSelectOneType: string | undefined
  mSelectId: string | undefined
  mSelectIds: (string | undefined)[]
  mSelectActive: unknown[]
}

export default function useSelectListen(canvasEditor: Editor) {
  const state = reactive<Selector>({
    mSelectMode: SelectMode.EMPTY,
    mSelectOneType: '',
    mSelectId: '', // 选择id
    mSelectIds: [], // 选择id
    mSelectActive: [],
  })

  const selectMulti = (e: fabric.Object[]) => {
    state.mSelectMode = SelectMode.MULTI
    state.mSelectId = ''
    state.mSelectIds = e.map(item => (item as any).id)
  }

  const selectCancel = () => {
    state.mSelectId = ''
    state.mSelectIds = []
    state.mSelectMode = SelectMode.EMPTY
    state.mSelectOneType = ''
  }

  const selectOne = (e: [fabric.Object]) => {
    state.mSelectMode = SelectMode.ONE
    state.mSelectActive = e
    if (e[0] && get(e[0], 'clip')) {
      selectCancel()
      // state.mSelectId = get(e[0], 'targetId');
      // state.mSelectOneType = get(e[0], 'targetType');
      // state.mSelectIds = e.map((item) => get(item, 'targetId'));
      return
    }
    if (!e[0]) {
      return
    }
    state.mSelectId = (e[0] as any).id
    state.mSelectOneType = e[0].type
    state.mSelectIds = e.map(item => (item as any).id)
  }

  onMounted(() => {
    canvasEditor.on(SelectEvent.ONE, selectOne)
    canvasEditor.on(SelectEvent.MULTI, selectMulti)
    canvasEditor.on(SelectEvent.CANCEL, selectCancel)
  })

  onBeforeMount(() => {
    canvasEditor.off(SelectEvent.ONE, selectOne)
    canvasEditor.off(SelectEvent.MULTI, selectMulti)
    canvasEditor.off(SelectEvent.CANCEL, selectCancel)
  })

  return {
    mixinState: state,
  }
}

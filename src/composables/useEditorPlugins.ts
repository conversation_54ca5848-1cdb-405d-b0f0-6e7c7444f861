import type { IEditor } from '@kuaitu/core'
import {
  AddBaseTypePlugin,
  AlignGuidLinePlugin,
  BarCodePlugin,
  CenterAlignPlugin,
  ControlsPlugin,
  CopyPlugin,
  DeleteHotKeyPlugin,
  DrawLinePlugin,
  DrawPolygonPlugin,
  DringPlugin,
  FlipPlugin,
  FreeDrawPlugin,
  GroupAlignPlugin,
  GroupPlugin,
  GroupTextEditorPlugin,
  HistoryPlugin,
  ImageStroke,
  LayerPlugin,
  LockPlugin,
  MaskPlugin,
  MoveHotKeyPlugin,
  PathTextPlugin,
  PolygonModifyPlugin,
  PsdPlugin,
  QrCodePlugin,
  ResizePlugin,
  RulerPlugin,
  SimpleClipImagePlugin,
  WaterMarkPlugin,
  WorkspacePlugin,
} from '@kuaitu/core'

export function useEditorPlugins(editor: IEditor) {
  const initPlugins = () => {
    editor
      .use(DringPlugin)
      .use(PolygonModifyPlugin)
      .use(AlignGuidLinePlugin)
      .use(ControlsPlugin)
      .use(CenterAlignPlugin)
      .use(LayerPlugin)
      .use(CopyPlugin)
      .use(MoveHotKeyPlugin)
      .use(DeleteHotKeyPlugin)
      .use(GroupPlugin)
      .use(DrawLinePlugin)
      .use(GroupTextEditorPlugin)
      .use(GroupAlignPlugin)
      .use(WorkspacePlugin)
      .use(HistoryPlugin)
      .use(FlipPlugin)
      .use(RulerPlugin)
      .use(DrawPolygonPlugin)
      .use(FreeDrawPlugin)
      .use(PathTextPlugin)
      // .use(SimpleClipImagePlugin)
      .use(BarCodePlugin)
      .use(QrCodePlugin)
      .use(WaterMarkPlugin)
      .use(PsdPlugin)
      .use(ImageStroke)
      // .use(ResizePlugin)
      .use(LockPlugin)
      .use(AddBaseTypePlugin)
      .use(MaskPlugin)
  }

  return {
    initPlugins,
  }
}

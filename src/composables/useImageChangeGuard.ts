import type { TextLayer } from '@/api/types'
import { t } from '@/language'
import { useImagesStore } from '@/stores/images'
import { useUnsavedChangesStore } from '@/stores/unsavedChanges'
import { ElMessageBox } from 'element-plus'

export enum ChangeType {
  TRANSLATION_REMARK = 'translation_remark',
  LAYER = 'layer',
}

export function useImageChangeGuard(changeType: ChangeType = ChangeType.TRANSLATION_REMARK) {
  const store = useImagesStore()

  // 是否正在重置中，用于防止重置时触发变更检测
  const isResetting = ref(false)

  // 重置变更追踪
  const resetChangeTracking = () => {
    isResetting.value = true
    store.images.forEach((img) => {
      img.istranslateChanged = false
    })
    nextTick(() => {
      isResetting.value = false
    })
  }

  // 检查图层是否有变更
  const checkLayerChanges = () => {
    // 如果正在重置中，不进行变更检测
    if (isResetting.value) {
      return false
    }

    let hasChanges = false

    if (changeType === ChangeType.TRANSLATION_REMARK) {
      // 检查翻译和备注的变更
      hasChanges = !!store.currentActiveImage?.layers?.some((layer) => {
        const original = store.currentActiveImage?.originalLayers?.find(
          originalLayer => originalLayer.id === layer.id,
        )
        if (!original)
          return true // 如果找不到原始数据，说明是新增的图层，应该视为有变更

        if (layer.type === 'text' && original.type === 'text') {
          return original.content !== layer.content || (original.remark || '') !== (layer.remark || '')
        }
        return false
      })
    }
    else if (changeType === ChangeType.LAYER) {
      // 检查图层结构的变更（包括图层数量、属性等）
      const currentLayers = store.currentActiveImage?.layers || []
      const originalLayers = store.currentActiveImage?.originalLayers || []

      // 检查图层数量是否变化
      if (currentLayers.length !== originalLayers.length) {
        hasChanges = true
      }
      else {
        // 检查每个图层的属性变化
        hasChanges = currentLayers.some((layer, index) => {
          const original = originalLayers[index]
          if (!original || original.id !== layer.id)
            return true

          // 检查基础属性
          if (
            layer.top !== original.top
            || layer.left !== original.left
            || layer.width !== original.width
            || layer.height !== original.height
            || layer.zIndex !== original.zIndex
          ) {
            return true
          }

          // 检查文本图层特有属性
          if (layer.type === 'text' && original.type === 'text') {
            return (
              layer.backgroundColor !== original.backgroundColor
              || layer.color !== original.color
              || layer.textAlign !== original.textAlign
              || layer.letterSpacing !== original.letterSpacing
              || layer.fontFamily !== original.fontFamily
              || layer.fontSize !== original.fontSize
              || layer.lineHeight !== original.lineHeight
              || layer.content !== original.content
              || layer.strokeWidth !== original.strokeWidth
              || layer.stroke !== original.stroke
              || layer.borderRadius !== original.borderRadius
              || layer.fill !== original.fill
              || layer.visible !== original.visible
            )
          }

          return false
        })
      }
    }

    if (hasChanges && store.currentActiveImage) {
      store.currentActiveImage.istranslateChanged = true
    }

    return hasChanges
  }

  // 提示保存变更
  const promptSaveChanges = async () => {
    if (!store.currentActiveImage?.istranslateChanged)
      return false

    try {
      const result = await ElMessageBox.confirm(
        t('dialog.unsaved_changes.message'),
        t('dialog.unsaved_changes.title'),
        {
          confirmButtonText: t('dialog.unsaved_changes.save'),
          cancelButtonText: t('dialog.unsaved_changes.cancel'),
          distinguishCancelAndClose: true,
          type: 'warning',
          showClose: true,
          closeOnClickModal: false,
          closeOnPressEscape: true,
          showCancelButton: true,
          showConfirmButton: true,
        },
      )
      return result === 'confirm'
    }
    catch {
      return false
    }
    finally {
      if (store.currentActiveImage) {
        store.currentActiveImage.istranslateChanged = false
      }
    }
  }

  // 图片切换守卫
  const guardImageChange = async (
    targetAction: () => void | Promise<void>,
  ) => {
    const hasChanges = checkLayerChanges()

    if (!hasChanges) {
      await targetAction()
      return false
    }

    const shouldSave = await promptSaveChanges()
    if (shouldSave) {
      // 如果用户选择保存，返回 true 让调用方先保存
      // 保存完成后再执行切换操作
      return true
    }
    else {
      // 如果用户选择不保存，先重置变更状态，再执行切换操作
      if (store.currentActiveImage)
        store.currentActiveImage.istranslateChanged = false
      await targetAction()
      return false
    }
  }

  return {
    resetChangeTracking,
    checkLayerChanges,
    guardImageChange,
  }
}

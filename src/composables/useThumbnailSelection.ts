import type { ImageType } from '@/api/types'
import type { Ref } from 'vue'
import { useDesignStore } from '@/stores/design'
import { useImagesStore } from '@/stores/images'
import { ref } from 'vue'

/**
 * 处理设计稿缩略图选择的通用逻辑
 * @param currentImageType 当前图片类型
 * @param options 配置选项
 * @returns 缩略图选择相关的方法和状态
 */
export function useThumbnailSelection(
  currentImageType: Ref<ImageType>,
  options: {
    /**
     * 在切换图片前执行的保存操作
     * @param imageId 当前图片ID
     * @returns Promise<boolean> 是否成功保存
     */
    beforeImageChange?: (imageId: string) => Promise<boolean>
    /**
     * 在切换图片后执行的清理操作
     */
    afterImageChange?: () => void
  } = {},
) {
  const imagesStore = useImagesStore()
  const designStore = useDesignStore()
  const previousImageId = ref<string | null>(null)

  /**
   * 处理缩略图选择
   * @param id 选中的设计稿ID
   */
  async function handleThumbnailSelected(id: string) {
    const oldId = imagesStore.activeImageId

    // 先保存旧图标记
    if (options.beforeImageChange && oldId && oldId !== '0') {
      await options.beforeImageChange(oldId)
    }

    // 执行清理操作
    options.afterImageChange?.()

    // 真正切换到新图
    imagesStore.activeImageId = id
    designStore.selectDesign(id) // 更新缩略图选中态
    previousImageId.value = oldId
  }

  /**
   * 切换到上一个设计稿
   */
  async function prevImage() {
    const oldId = imagesStore.activeImageId
    if (options.beforeImageChange && oldId && oldId !== '0')
      await options.beforeImageChange(oldId)
    options.afterImageChange?.()
    // 使用 useDesignViewer 中的 prevImage 逻辑
    const designs = designStore.designInfoMap[currentImageType.value]
    if (designs.length === 0)
      return

    const currentIndex = designs.findIndex(d => d.designId === imagesStore.activeImageId)
    const prevIndex = currentIndex > 0 ? currentIndex - 1 : designs.length - 1
    const prevDesignId = designs[prevIndex]?.designId

    if (prevDesignId) {
      imagesStore.activeImageId = prevDesignId
      designStore.selectDesign(prevDesignId)
      previousImageId.value = oldId
    }
  }

  /**
   * 切换到下一个设计稿
   */
  async function nextImage() {
    const oldId = imagesStore.activeImageId
    if (options.beforeImageChange && oldId && oldId !== '0')
      await options.beforeImageChange(oldId)
    options.afterImageChange?.()

    // 使用 useDesignViewer 中的 nextImage 逻辑
    const designs = designStore.designInfoMap[currentImageType.value]
    if (designs.length === 0)
      return

    const currentIndex = designs.findIndex(d => d.designId === imagesStore.activeImageId)
    const nextIndex = currentIndex < designs.length - 1 ? currentIndex + 1 : 0
    const nextDesignId = designs[nextIndex]?.designId

    if (nextDesignId) {
      imagesStore.activeImageId = nextDesignId
      designStore.selectDesign(nextDesignId)
      previousImageId.value = oldId
    }
  }

  return {
    handleThumbnailSelected,
    prevImage,
    nextImage,
    previousImageId,
  }
}

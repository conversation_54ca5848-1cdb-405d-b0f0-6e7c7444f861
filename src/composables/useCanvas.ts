import type { IEditor } from '@kuaitu/core'
import { fabric } from 'fabric'

export function useCanvas() {
  const initCanvas = () => {
    return new fabric.Canvas('canvas', {
      fireRightClick: true,
      stopContextMenu: true,
      controlsAboveOverlay: true,
    })
  }

  const setCanvasSize = (editor: IEditor, width: number, height: number) => {
    editor.setSize(width, height)
  }

  const hideControls = (canvas: fabric.Canvas) => {
    canvas.forEachObject((object) => {
      object.set({
        hasControls: false,
        hasBorders: false,
      })
    })

    canvas.selection = false
    canvas.skipTargetFind = true
    canvas.interactive = false
    canvas.controlsAboveOverlay = false
    canvas.renderAll()
  }

  return {
    initCanvas,
    setCanvasSize,
    hideControls,
  }
}

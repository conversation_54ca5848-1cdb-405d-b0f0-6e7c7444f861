import type { ImageType } from '@/api/types'
import { useCanvas } from '@/composables/useCanvas'
import { useEditorPlugins } from '@/composables/useEditorPlugins'
import { useDesignStore } from '@/stores/design'
import { useImagesStore } from '@/stores/images'
import { initFabricExtensions } from '@/utils/fabricExtensions'
import { getImageTypeFromTag } from '@/utils/imageUtils'
import { renderJsonData } from '@/utils/jsonRenderer'
import Editor, { type IEditor } from '@kuaitu/core'
import { ElMessage } from 'element-plus'
import { fabric } from 'fabric'
import { computed, nextTick, onMounted, onUnmounted, ref, type Ref, watch } from 'vue'

export function useDesignViewer(currentTag: Ref<string>, opts = { readonly: false }) {
  const imagesStore = useImagesStore()
  const designStore = useDesignStore()
  const isMounted = ref(false)
  const canvasEditor = new Editor() as IEditor
  const { initCanvas } = useCanvas()
  const { initPlugins } = useEditorPlugins(canvasEditor)

  const currentImageType = computed(() => getImageTypeFromTag(currentTag.value) as ImageType)
  const currentMarkers = ref<{ id: number, content: string, rect: any, fabricObject?: fabric.Object }[]>([])
  const highlightedMarkerId = ref<number | null>(null)

  initFabricExtensions()

  /**
   * 安全地清除画布上的非workspace对象，并确保workspace存在
   * @param defaultWidth 默认workspace宽度
   * @param defaultHeight 默认workspace高度
   * @returns 是否成功执行
   */
  function safelyCleanCanvas(defaultWidth = 600, defaultHeight = 400): boolean {
    if (!canvasEditor.fabricCanvas)
      return false

    const canvas = canvasEditor.fabricCanvas
    const objects = canvas.getObjects()
    const workspaceObj = objects.find(item => (item as any).id === 'workspace')

    objects.forEach((obj) => {
      if ((obj as any).id !== 'workspace') {
        canvas.remove(obj)
      }
    })

    if (!workspaceObj) {
      const tempWorkspace = new fabric.Rect({
        fill: 'rgba(255,255,255,1)',
        width: defaultWidth,
        height: defaultHeight,
        strokeWidth: 0,
        selectable: false,
        hasControls: false,
      } as fabric.IRectOptions)

      ;(tempWorkspace as any).id = 'workspace'
      tempWorkspace.hoverCursor = 'default'
      canvas.add(tempWorkspace)
    }

    canvasEditor.setSize(defaultWidth, defaultHeight)
    canvas.renderAll()
    return true
  }

  /**
   * 加载设计稿图片
   * @param designLink 设计稿图片链接
   * @param remark 标记数据（JSON字符串）
   */
  async function loadDesignImage(designLink: string, remark?: string) {
    if (!canvasEditor.fabricCanvas || !isMounted.value)
      return
    try {
      await renderJsonData(
        canvasEditor,
        canvasEditor.fabricCanvas,
        {
          backgroundImg: designLink,
          layers: remark ? JSON.parse(remark) : [],
        },
        false,
        true,
      )
      adjustCanvasSize()
    }
    catch (error) {
      console.error('加载设计稿图片失败:', error)

      safelyCleanCanvas(600, 400)

      ElMessage.error('加载设计稿图片失败')
    }
  }

  /**
   * 调整画布大小以适应容器
   */
  function adjustCanvasSize() {
    if (!canvasEditor.fabricCanvas)
      return
    const workspaceEl = document.getElementById('workspace')
    if (!workspaceEl)
      return
    const containerWidth = workspaceEl.clientWidth
    const containerHeight = workspaceEl.clientHeight
    const canvasWidth = canvasEditor.fabricCanvas.getWidth()
    const canvasHeight = canvasEditor.fabricCanvas.getHeight()
    const scaleX = containerWidth / canvasWidth
    const scaleY = containerHeight / canvasHeight
    const scale = Math.min(scaleX, scaleY) * 0.9
    const canvas = document.getElementById('canvas') as HTMLCanvasElement
    if (canvas) {
      canvas.style.transform = `scale(${scale})`
      canvas.style.transformOrigin = 'center center'
    }
  }

  /**
   * 处理窗口大小变化
   */
  function handleResize() {
    const designs = designStore.designInfoMap[currentImageType.value]
    const currentDesign = designs.find(d => d.designId === imagesStore.activeImageId)
    if (currentDesign && currentDesign.designLink) {
      loadDesignImage(currentDesign.designLink, currentDesign.remark)
    }
    else {
      adjustCanvasSize()
    }
  }

  /**
   * 切换到上一张设计图
   */
  function prevImage() {
    const designs = designStore.designInfoMap[currentImageType.value]
    if (designs.length === 0)
      return
    const currentIndex = designs.findIndex(d => d.designId === imagesStore.activeImageId)
    const prevIndex = currentIndex > 0 ? currentIndex - 1 : designs.length - 1
    const prevDesignId = designs[prevIndex]?.designId
    if (prevDesignId) {
      imagesStore.activeImageId = prevDesignId
    }
  }

  /**
   * 切换到下一张设计图
   */
  function nextImage() {
    const designs = designStore.designInfoMap[currentImageType.value]
    if (designs.length === 0)
      return
    const currentIndex = designs.findIndex(d => d.designId === imagesStore.activeImageId)
    const nextIndex = currentIndex < designs.length - 1 ? currentIndex + 1 : 0
    const nextDesignId = designs[nextIndex]?.designId
    if (nextDesignId) {
      imagesStore.activeImageId = nextDesignId
    }
  }

  /**
   * 解析标记数据
   * @param remark 标记数据（JSON字符串）
   * @returns 解析后的标记数组
   */
  const parseMarkers = (remark?: string): any[] => {
    if (!remark)
      return []
    try {
      const parsedLayers = JSON.parse(remark)
      if (Array.isArray(parsedLayers)) {
        return parsedLayers.map((layer, index) => ({
          id: layer.id || index,
          content: layer.remark || '',
          rect: {
            top: layer.top,
            left: layer.left,
            width: layer.width,
            height: layer.height,
          },
        }))
      }
    }
    catch (error) {
      console.error('解析标记数据失败:', error)
    }
    return []
  }

  /**
   * 高亮显示标记
   * @param markerId 要高亮的标记ID，为null时取消高亮
   */
  function highlightMarker(markerId: number | null) {
    if (!canvasEditor.fabricCanvas)
      return

    currentMarkers.value.forEach((marker) => {
      if (marker.fabricObject) {
        marker.fabricObject.set({
          stroke: '#ff0000',
          strokeWidth: 2,
          fill: 'rgba(255, 0, 0, 0.2)',
        })
      }
    })
    highlightedMarkerId.value = markerId

    if (markerId !== null) {
      const marker = currentMarkers.value.find(m => m.id === markerId)
      if (marker && marker.fabricObject) {
        marker.fabricObject.set({
          stroke: '#0000ff',
          strokeWidth: 3,
          fill: 'rgba(0, 0, 255, 0.3)',
        })
        canvasEditor.fabricCanvas.bringToFront(marker.fabricObject)
      }
    }
    canvasEditor.fabricCanvas.renderAll()
  }

  /**
   * 创建标记对象并添加到画布上
   */
  function createMarkerObjects() {
    if (!canvasEditor.fabricCanvas)
      return
    /** 先把旧的 marker 清理掉，避免叠加 */
    canvasEditor.fabricCanvas.getObjects().forEach((obj) => {
      const id = (obj as any).id as string | undefined
      if (id?.startsWith('marker-')) {
        canvasEditor.fabricCanvas!.remove(obj)
      }
    })

    highlightedMarkerId.value = null
    currentMarkers.value.forEach((marker) => {
      const rect = new fabric.Rect({
        left: marker.rect.left,
        top: marker.rect.top,
        width: marker.rect.width,
        height: marker.rect.height,
        fill: 'rgba(255, 0, 0, 0.2)',
        stroke: '#ff0000',
        strokeWidth: 2,
        selectable: !opts.readonly,
        evented: !opts.readonly,
      })
      // 给画布对象恢复同名 id，后续可直接查重 / 选中
      ;(rect as any).id = typeof marker.id === 'string'
        ? marker.id
        : `marker-${marker.id}`
      marker.fabricObject = rect
      canvasEditor.fabricCanvas?.add(rect)
    })
    canvasEditor.fabricCanvas.renderAll()
  }

  // 监听图片类型变化，切换时重新选择首图
  watch(
    () => currentImageType.value,
    async (newType) => {
      if (imagesStore.activeImageId === '0' && designStore.designInfoMap[newType]?.length > 0) {
        const firstDesign = designStore.designInfoMap[newType][0]
        if (firstDesign.designId) {
          imagesStore.activeImageId = firstDesign.designId
        }
      }
    },
  )

  /**
   * 监听活动图片ID变化
   */
  watch(
    () => imagesStore.activeImageId,
    async (newId, _oldId) => {
      if (!newId || newId === '0' || !canvasEditor.fabricCanvas || !isMounted.value) {
        safelyCleanCanvas(600, 400)
        currentMarkers.value = []
        return
      }

      const designs = designStore.designInfoMap[currentImageType.value] || []
      const currentDesign = designs.find(d => d.designId === newId)

      if (currentDesign && currentDesign.designLink) {
        currentMarkers.value = parseMarkers(currentDesign.remark)
        await loadDesignImage(currentDesign.designLink, currentDesign.remark)
        createMarkerObjects()
      }
      else {
        safelyCleanCanvas(600, 400)

        currentMarkers.value = []
        if (currentDesign) {
          console.warn(`找到设计 ${newId} 但没有设计链接`)
        }
        else {
          console.warn(`在类型 ${currentImageType.value} 中未找到设计 ${newId}`)
        }
      }
    },
    { immediate: false },
  )

  /**
   * 处理初始选择逻辑
   */
  const processInitialSelection = async () => {
    await nextTick()
    const designs = designStore.designInfoMap[currentImageType.value] || []
    const activeId = imagesStore.activeImageId

    if (activeId && activeId !== '0' && designs.length > 0) {
      const currentDesign = designs.find(d => d.designId === activeId)
      if (currentDesign && currentDesign.designLink) {
        await loadDesignImage(currentDesign.designLink, currentDesign.remark)
        createMarkerObjects()
      }
      else {
        if (designs.length > 0 && designs[0].designId) {
          imagesStore.activeImageId = designs[0].designId
        }
      }
    }
    else if (designs.length > 0 && (!activeId || activeId === '0')) {
      if (designs[0].designId) {
        imagesStore.activeImageId = designs[0].designId
      }
    }
  }

  onMounted(async () => {
    const canvas = initCanvas()
    canvasEditor.init(canvas)
    initPlugins()

    await nextTick()

    // 检查workspace是否已初始化
    const workspace = canvasEditor.fabricCanvas?.getObjects().find(item => (item as any).id === 'workspace')
    if (!workspace) {
      console.warn('Workspace未正确初始化，尝试重新初始化插件')

      initPlugins()
      await nextTick()
    }

    isMounted.value = true

    window.addEventListener('resize', handleResize)

    await imagesStore.init()

    const initialTransId = imagesStore.getCurrentTransId
    let dataFetched = false

    /**
     * 获取设计数据并处理初始选择
     */
    const fetchData = async (id: string | undefined) => {
      if (id && id !== '-1' && !dataFetched) {
        await designStore.fetchAllDesignInfoTypes()
        dataFetched = true

        await nextTick()

        processInitialSelection()
      }
    }

    await fetchData(initialTransId)

    if (!dataFetched) {
      const unwatchTransId = watch(
        () => imagesStore.getCurrentTransId,
        async (newTransId) => {
          if (newTransId && newTransId !== '-1') {
            await fetchData(newTransId)
            unwatchTransId()
          }
        },
        { immediate: false },
      )
    }
  })

  onUnmounted(() => {
    window.removeEventListener('resize', handleResize)
    if (canvasEditor.fabricCanvas) {
      canvasEditor.destory()
    }
  })

  return {
    canvasEditor,
    currentImageType,
    isMounted,
    prevImage,
    nextImage,
    currentMarkers,
    highlightMarker,
    highlightedMarkerId,
  }
}

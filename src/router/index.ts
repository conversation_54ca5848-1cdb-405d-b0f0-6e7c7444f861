import type { RouteRecordRaw } from 'vue-router'
import Layout from '@/components/Layout/Layout.vue'
import { createRouter, createWebHashHistory } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'Layout',
    redirect: '/upload',
    component: Layout,
    children: [
      {
        path: 'login',
        name: 'Login',
        meta: { affix: true, title: '登录' },
        component: () => import('@/views/Login/Login.vue'),
      },
      {
        path: 'translate',
        name: 'Translate',
        meta: { affix: true, title: '翻译校验' },
        component: () => import('@/views/Translate/Translate.vue'),
      },
      {
        path: 'design',
        name: 'Design',
        meta: { affix: true, title: '设计' },
        component: () => import('@/views/Design/Design.vue'),
      },
      {
        path: 'upload',
        name: 'Upload',
        meta: { affix: true, title: '上传' },
        component: () => import('@/views/Upload/Upload.vue'),
      },
      {
        path: 'design-check',
        name: 'DesignCheck',
        meta: { affix: true, title: '设计校验' },
        component: () => import('@/views/DesignCheck/DesignCheck.vue'),
      },
    ],
  },
]
routes.push({ path: '/:pathMatch(.*)', redirect: '/' })

const router = createRouter({
  history: createWebHashHistory(),
  routes,
})
export default router

<script setup lang="ts">
import { addPhrase, translate } from '@/api/imageTranslationApi'
import { usePhrasesStore } from '@/stores/phrases'
import { Close } from '@element-plus/icons-vue'
import { useI18n } from 'vue-i18n'

const props = defineProps<{
  modelValue: boolean
  initialText: string
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void
  (e: 'translateResult', value: string): void
  (e: 'close'): void
}>()

const { t } = useI18n()

// 提示语store
const phrasesStore = usePhrasesStore()
// 选中的提示语
const selectedPhrase = ref<number | undefined>(undefined)
// 要翻译的内容
const translationContent = ref('')
// 输入的提示语
const inputPrompt = ref('')
// 翻译结果
const translationResult = ref('')
// 是否显示添加提示语对话框
const showAddPhraseDialog = ref(false)
// 新提示语标题
const newPhraseTitle = ref('')
// 翻译按钮loading状态
const isTranslating = ref(false)
// 输入框引用
const inputPromptRef = ref()
// 表单ref
const addPhraseFormRef = ref()
// 表单规则
const addPhraseRules = {
  title: [
    { required: true, message: t('dialog.ai_translate.phrase_title_required'), trigger: 'blur' },
  ],
  content: [
    { required: true, message: t('dialog.ai_translate.prompt_required'), trigger: 'blur' },
  ],
}

// 初始化数据
async function initData() {
  await phrasesStore.fetchPhrases()
  // 默认选中第一个提示语
  if (phrasesStore.phrases.length > 0) {
    selectedPhrase.value = phrasesStore.phrases[0].id
    inputPrompt.value = phrasesStore.phrases[0].content
  }
}

// 监听弹窗显示
watch(() => props.modelValue, (val) => {
  if (val) {
    translationContent.value = props.initialText
    inputPrompt.value = ''
    translationResult.value = ''
    initData()
    // 在下一个tick时聚焦输入框
    nextTick(() => {
      inputPromptRef.value?.focus()
    })
  }
})

// 监听提示语选择
watch(selectedPhrase, (val) => {
  if (val) {
    const phrase = phrasesStore.phrases.find(p => p.id === val)
    if (phrase) {
      inputPrompt.value = phrase.content
    }
  }
})

// 处理翻译
async function handleTranslate() {
  if (!translationContent.value) {
    ElMessage.warning(t('dialog.ai_translate.content_required'))
    return
  }
  if (!inputPrompt.value) {
    ElMessage.warning(t('dialog.ai_translate.prompt_required'))
    return
  }

  isTranslating.value = true
  try {
    const res = await translate({
      prompt: inputPrompt.value,
      phraseId: selectedPhrase.value ? Number(selectedPhrase.value) : undefined,
      source: translationContent.value,
    })
    if (res.success) {
      translationResult.value = res.data
    }
  }
  finally {
    isTranslating.value = false
  }
}

// 应用翻译结果
function applyTranslation() {
  emit('translateResult', translationResult.value)
  emit('update:modelValue', false)
}

// 添加到提示语
async function addToPhrase() {
  if (!addPhraseFormRef.value)
    return

  await addPhraseFormRef.value.validate(async (valid: boolean) => {
    if (valid) {
      const res = await addPhrase({
        title: newPhraseTitle.value,
        content: inputPrompt.value,
      })
      if (res.success) {
        ElMessage.success(t('dialog.ai_translate.add_phrase_success'))
        showAddPhraseDialog.value = false
        newPhraseTitle.value = ''
        initData()
      }
    }
  })
}

// 关闭弹窗
function handleClose() {
  emit('close')
}
</script>

<template>
  <div class="ai-translate-dialog">
    <!-- 标题栏 -->
    <div class="dialog-header">
      <span class="title">{{ $t('dialog.ai_translate.title') }}</span>
      <el-button type="primary" link :icon="Close" font-size="20px" @click="handleClose" />
    </div>

    <div class="dialog-content">
      <div class="dialog-form">
        <!-- 提示语选择 -->
        <el-select
          v-model="selectedPhrase" filterable clearable :teleported="false" :placeholder="$t('dialog.ai_translate.common_phrases')"
          class="phrase-select"
        >
          <el-option v-for="item in phrasesStore.phrases" :key="item.id" :label="item.title" :value="item.id" />
        </el-select>

        <!-- 提示词内容 -->
        <div class="input-section">
          <el-input
            ref="inputPromptRef" v-model="inputPrompt" type="textarea" :rows="4" :maxlength="500"
            show-word-limit :placeholder="$t('dialog.ai_translate.source_placeholder')"
          />
        </div>

        <!-- 要翻译的内容 -->
        <div class="input-section">
          <el-input
            v-model="translationContent" type="textarea" :rows="4" :maxlength="500" show-word-limit
            :placeholder="$t('dialog.ai_translate.translation_content_placeholder')"
          />
        </div>

        <!-- 翻译结果 -->
        <div class="result-section">
          <el-input
            v-model="translationResult" type="textarea" :rows="4" :maxlength="1000" show-word-limit
            :placeholder="$t('dialog.ai_translate.target_placeholder')"
          />
        </div>
      </div>
      <div class="trans">
        <!-- 添加提示语按钮 -->
        <el-button type="primary" plain @click="showAddPhraseDialog = true">
          {{ $t('dialog.ai_translate.add_phrase') }}
        </el-button>
        <!-- 翻译和应用按钮 -->
        <el-button type="primary" class="translate-btn" :loading="isTranslating" @click="handleTranslate">
          {{ $t('dialog.ai_translate.translate') }}
        </el-button>
        <el-button type="primary" :disabled="!translationResult" class="apply-btn" @click="applyTranslation">
          {{ $t('dialog.ai_translate.apply') }}
        </el-button>
      </div>
    </div>
  </div>

  <!-- 添加提示语对话框 -->
  <el-dialog
    v-model="showAddPhraseDialog" :title="$t('dialog.ai_translate.add_phrase')" width="400px" append-to-body
    close-on-press-escape
  >
    <el-form
      ref="addPhraseFormRef" :model="{ title: newPhraseTitle, content: inputPrompt }" :rules="addPhraseRules"
      label-position="right" label-width="100px"
    >
      <el-form-item :label="$t('dialog.ai_translate.phrase_title')" prop="title">
        <el-input
          v-model="newPhraseTitle" :placeholder="$t('dialog.ai_translate.phrase_title')" maxlength="15"
          show-word-limit
        />
      </el-form-item>
      <el-form-item :label="$t('dialog.ai_translate.phrase_content')" prop="content">
        <el-input
          v-model="inputPrompt" type="textarea" :rows="3"
          :placeholder="$t('dialog.ai_translate.phrase_content')"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="showAddPhraseDialog = false">
        {{ $t('dialog.ai_translate.cancel') }}
      </el-button>
      <el-button type="primary" @click="addToPhrase">
        {{ $t('dialog.ai_translate.confirm') }}
      </el-button>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
.ai-translate-dialog {
  display: flex;
  flex-direction: column;
  gap: 4px;

  .dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    border-bottom: 1px solid #e7e7e7;

    .title {
      font-size: 16px;
      font-weight: bold;
    }

    :deep(.el-button) {
      font-size: 20px;
    }
  }

  .dialog-content {
    display: flex;
    gap: 16px;

    .dialog-form {
      flex: 1;
      min-width: 280px;
      display: flex;
      flex-direction: column;

      .section-title {
        font-size: 14px;
        color: #606266;
        margin-bottom: 8px;
      }

      .input-section,
      .result-section {
        margin-top: 16px;
      }
    }

    .trans {
      display: flex;
      flex-direction: column;
      align-items: start;
      justify-content: space-between;
      gap: 16px;
      max-width: 100px;

      .holder {
        height: 77px;
      }

      .translate-btn {
        margin-left: 0;
      }

      .apply-btn {
        margin-left: 0;
      }

      :deep(.el-button):first-child {
        padding: 8px;
        span {
          word-break: break-word;
          word-wrap: break-word;
          white-space: normal;
        }
      }
    }

    .add {
      display: flex;
      flex-direction: column;
      justify-content: center;
    }
  }

  .phrase-select {
    width: 100%;
  }

  .input-section {
    position: relative;

    .translate-btn {
      position: absolute;
      right: 10px;
      bottom: 10px;
    }
  }

  .result-section {
    display: flex;
    flex-direction: column;
    gap: 10px;

    .action-buttons {
      display: flex;
      gap: 10px;
      justify-content: flex-end;
    }
  }
}
</style>

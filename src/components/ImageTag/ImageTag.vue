<script lang="ts" setup>
import type { Layer, TextLayer } from '@/api/types'
import type { WorkflowAction } from '@/constants/workflowActions'
import type { Justification, LayerTextData, Psd } from 'ag-psd'
import { downloadPackage, TranslateStatus } from '@/api/imageTranslationApi'
import { getDetailById, process as processTicket, query as queryTicketList } from '@/api/ticketApi'
import { ImageType, TicketInstProcessReqVOOpResultEnum as OpResult, TicketInstProcessReqVOOpTypeEnum as OpType } from '@/api/types'
import { useImageChangeGuard } from '@/composables/useImageChangeGuard'
import { pageActionMap } from '@/constants/workflowActions'
import { useDesignStore } from '@/stores/design'
import { useImagesStore } from '@/stores/images'
import { useSpuStore } from '@/stores/spu'
import { useUserStore } from '@/stores/user'
import { downloadImageAsBlob, extractOssFilename, getCurrentImageTypeString } from '@/utils/utils'
import { ArrowDown } from '@element-plus/icons-vue'
import { writePsd } from 'ag-psd'
import chroma from 'chroma-js'
import { ElMessage, ElMessageBox } from 'element-plus'
import { saveAs } from 'file-saver'
import { useI18n } from 'vue-i18n'
import { useRoute, useRouter } from 'vue-router'

interface ImageElement {
  element: HTMLImageElement
  layers: Layer[]
  blob: Blob
}

const props = defineProps<{
  modelValue: string
}>()

const emit = defineEmits<{
  'update:modelValue': [value: string]
  'downloadWithBg': []
  'downloadWithoutBg': []
  'downloadAll': []
  'back': []
  'contentSelected': []
}>()

const { t } = useI18n()

const imagesStore = useImagesStore()
const designStore = useDesignStore()
const route = useRoute()
const router = useRouter()
const spuStore = useSpuStore()
const userStore = useUserStore()
const { guardImageChange } = useImageChangeGuard()

// 工单节点状态
const currentNodeId = ref<string>()
const ticketInstId = ref<number>()
const visibleActions = ref<WorkflowAction[]>([])
const actionProcessMap: Record<WorkflowAction, {
  opType: OpType
  opResult?: OpResult
}> = {
  /* 翻译校验页 */
  submit_verification: { opType: OpType.SUBMIT },
  verification_approve: { opType: OpType.APPROVE, opResult: OpResult.APPROVED }, // 审核通过
  verification_reject: { opType: OpType.APPROVE, opResult: OpResult.REJECTED }, // 审核不通过
  corrected: { opType: OpType.SUBMIT },

  /* 设计页 */
  submit_confirm: { opType: OpType.SUBMIT },

  /* 设计校验页 */
  submit_text_check_correct: { opType: OpType.APPROVE, opResult: OpResult.APPROVED },
  submit_text_check_reject: { opType: OpType.APPROVE, opResult: OpResult.REJECTED },
  confirmed: { opType: OpType.APPROVE, opResult: OpResult.APPROVED },
  return_for_correction: { opType: OpType.APPROVE, opResult: OpResult.REJECTED },

  get_images: { opType: OpType.SUBMIT }, // 占位，无实际调用
}

const currentPath = computed(() => route?.path ?? '/upload')
const isDesign = computed(() => currentPath.value === '/design')
const isTranslate = computed(() => currentPath.value === '/translate')
const isDesignCheck = computed(() => currentPath.value === '/design-check')

const isDesignRelatedPage = computed(() => {
  return route.path === '/design' || route.path === '/design-check'
})

const tags = computed(() => [
  { label: t('components.image_tag.main_image'), value: 'mainImg', type: ImageType.MAIN },
  { label: t('components.image_tag.detail'), value: 'detail', type: ImageType.DETAIL },
  { label: t('components.image_tag.sku'), value: 'sku', type: ImageType.SKU },
  { label: t('components.image_tag.installation'), value: 'installation', type: ImageType.INSTALLATION },
  { label: t('components.image_tag.content'), value: 'content', type: '4' },
])

const tagCounts = computed(() => {
  const counts = {
    mainImg: 0,
    detail: 0,
    sku: 0,
    installation: 0,
  }

  if (isDesignRelatedPage.value) {
    // 在设计或设计校验页面，显示设计稿数量
    counts.mainImg = designStore.getDesignInfoByType(ImageType.MAIN).value.length
    counts.detail = designStore.getDesignInfoByType(ImageType.DETAIL).value.length
    counts.sku = designStore.getDesignInfoByType(ImageType.SKU).value.length
    counts.installation = designStore.getDesignInfoByType(ImageType.INSTALLATION).value.length
  }
  else {
    // 在其他页面，显示翻译图片数量
    counts.mainImg = imagesStore.images.filter(img => img.type === ImageType.MAIN).length
    counts.detail = imagesStore.images.filter(img => img.type === ImageType.DETAIL).length
    counts.sku = imagesStore.images.filter(img => img.type === ImageType.SKU).length
    counts.installation = imagesStore.images.filter(img => img.type === ImageType.INSTALLATION).length
  }
  return counts
})
const saveChanges = inject('saveChanges', async () => { })
const saveMarkers = inject<(showValidationMessage?: boolean, showSuccessMessage?: boolean) => Promise<boolean>>('saveMarkers', async () => false)

// 批准设计文案检查
async function approveDesignTextCheck() {
  try {
    // 保存标记
    await saveMarkers(false, false)
    // 执行通过操作
    await handleWorkflowAction('submit_text_check_correct')
    ElMessage.success(t('page.design.approveSuccess'))
  }
  catch (error) {
    console.error('保存设计标记失败:', error)
    ElMessage.error(t('upload.operationFailed'))
  }
}

// 拒绝设计文案检查
async function rejectDesignTextCheck() {
  try {
    // 保存标记
    await saveMarkers(false, false)
    // 执行不通过操作
    await handleWorkflowAction('submit_text_check_reject')
    ElMessage.success(t('page.design.rejectSuccess'))
  }
  catch (error) {
    console.error('保存设计标记失败:', error)
    ElMessage.error(t('upload.operationFailed'))
  }
}

// 处理标签切换
async function handleTagChange(newValue: string | number | boolean | undefined) {
  if (typeof newValue !== 'string') {
    return
  }

  // 如果在设计校验页面，切换标签前先保存标记
  if (currentPath.value === '/design-check' && props.modelValue !== 'content' && newValue !== props.modelValue) {
    await saveMarkers()
  }

  // 如果选择了文案部分
  if (newValue === 'content') {
    emit('update:modelValue', newValue)
    emit('contentSelected')
    return
  }

  const tag = tags.value.find(t => t.value === newValue)
  if (tag && imagesStore.transId !== '-1') {
    // 确保 tag.type 是 ImageType 类型
    if (tag.type === '4') {
      emit('update:modelValue', newValue)
      return
    }

    imagesStore.currentImageType = tag.type as ImageType
    const shouldSave = await guardImageChange(async () => { })
    if (shouldSave) {
      await saveChanges()
    }

    emit('update:modelValue', newValue)
    imagesStore.switchImageType(tag.type as ImageType)
    imagesStore.stopPolling()

    // 判断当前类型的图片之前是否已经轮询成功过
    if (imagesStore.images.filter(img => img.type === tag.type).length > 0
      && imagesStore.images.filter(img => img.type === tag.type).every(img => img.status !== TranslateStatus.SUCCESS)) {
      imagesStore.startPolling(imagesStore.transId, tag.type as ImageType)
    }
  }
  else {
    emit('update:modelValue', newValue)
  }
}

function back() {
  router.push({ path: '/upload' })
}

const isAllPngDownloading = ref(false)

async function downloadAllPng() {
  try {
    isAllPngDownloading.value = true
    const { blob, filename } = await downloadPackage(imagesStore.transId)

    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.style.display = 'none'
    document.body.appendChild(a)
    a.href = url
    a.download = filename
    a.click()

    window.URL.revokeObjectURL(url)
    document.body.removeChild(a)
  }
  catch (error) {
    console.error('Download failed:', error)
  }
  finally {
    isAllPngDownloading.value = false
  }
}

const isConcatDownloading = ref(false)
async function downloadConcatenatedImages() {
  const currentTag = tags.value.find(t => t.value === props.modelValue)
  if (!currentTag) {
    ElMessage.warning('请先选择图片类型')
    return
  }

  try {
    isConcatDownloading.value = true
    await imagesStore.downloadAllOriginalImages()
    // 获取当前类型的所有图片
    const currentTypeImages = imagesStore.images.filter(img => img.type === currentTag.type && img.originalImageBlob)

    if (currentTypeImages.length === 0) {
      ElMessage.warning('没有可下载的图片')
      return
    }

    // 创建canvas来拼接图片
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    if (!ctx) {
      ElMessage.error('创建canvas失败')
      return
    }

    // 计算总高度和最大宽度
    let totalHeight = 0
    let maxWidth = 0
    const imageElements: HTMLImageElement[] = []

    // 一个个顺序加载图片以获取尺寸
    for (const img of currentTypeImages) {
      if (!img.originalImageBlob)
        continue

      const imgElement = new Image()
      const imageUrl = URL.createObjectURL(img.originalImageBlob)

      await new Promise((resolve) => {
        imgElement.onload = () => {
          totalHeight += imgElement.height
          maxWidth = Math.max(maxWidth, imgElement.width)
          imageElements.push(imgElement)
          resolve(null)
        }
        imgElement.onerror = () => {
          resolve(null)
        }
        imgElement.src = imageUrl
      })
    }

    // 设置canvas尺寸
    canvas.width = maxWidth
    canvas.height = totalHeight

    // 在canvas上绘制图片
    let currentY = 0
    imageElements.forEach((imgElement) => {
      // 计算水平居中的x坐标
      const x = (maxWidth - imgElement.width) / 2
      ctx.drawImage(imgElement, x, currentY)
      currentY += imgElement.height
    })

    // 转换为blob并下载
    canvas.toBlob((blob) => {
      if (!blob) {
        ElMessage.error('生成图片失败')
        return
      }

      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `${spuStore.spu}_${getCurrentImageTypeString(imagesStore.currentImageType)}.png`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)

      // 清理创建的URL
      imageElements.forEach((img) => {
        if (img.src.startsWith('blob:')) {
          URL.revokeObjectURL(img.src)
        }
      })
    }, 'image/png')
  }
  catch (error) {
    console.error('Download concatenated images failed:', error)
    ElMessage.error('下载失败')
  }
  finally {
    isConcatDownloading.value = false
  }
}

async function downloadWithPsd() {
  try {
    const currentTag = tags.value.find(t => t.value === props.modelValue)
    if (!currentTag) {
      ElMessage.warning('请先选择图片类型')
      return
    }

    const currentTypeImages = imagesStore.images.filter(img => img.type === currentTag.type)

    if (currentTypeImages.length === 0) {
      ElMessage.warning('没有可下载的图片')
      return
    }

    let totalHeight = 0
    let maxWidth = 0
    const imageElements: ImageElement[] = []

    // 一个个顺序加载图片以获取尺寸
    for (const img of currentTypeImages) {
      let imgBlob: Blob | null = null
      let hasBgBlob = true

      try {
        imgBlob = img.backgroundImgBlob || null
        if (!imgBlob) {
          hasBgBlob = !!img.backgroundImg
          imgBlob = await downloadImageAsBlob(img.backgroundImg || img.image)
        }
        if (!imgBlob || imgBlob.size === 0) {
          console.warn('图片 blob 无效或为空，跳过:', img)
          continue
        }

        const imgElement = new Image()
        imgElement.dataset.filename = hasBgBlob ? img.backgroundImgFilename : `${extractOssFilename(img.image)}(解析失败)`
        const imageUrl = URL.createObjectURL(imgBlob)

        try {
          await Promise.race([
            new Promise((resolve, reject) => {
              imgElement.onload = () => {
                totalHeight += imgElement.height
                maxWidth = Math.max(maxWidth, imgElement.width)
                imageElements.push({
                  element: imgElement,
                  layers: img.layers || [],
                  blob: imgBlob!,
                })
                resolve(null)
              }
              imgElement.onerror = () => {
                reject(new Error(`加载图片失败: ${imageUrl}`))
              }
              imgElement.src = imageUrl
            }),
            new Promise((_, reject) =>
              setTimeout(() => reject(new Error('图片加载超时')), 40000),
            ),
          ])
        }
        catch (error) {
          console.warn('处理图片失败:', error)
        }
        finally {
          URL.revokeObjectURL(imageUrl)
        }
      }
      catch (error) {
        console.warn('下载或处理图片失败:', error)
        continue
      }
    }

    if (imageElements.length === 0) {
      ElMessage.warning('没有成功处理任何图片')
      return
    }

    const psd: Psd = {
      width: maxWidth,
      height: totalHeight,
      children: [],
    }

    // 为每个图片创建单独的图层
    let currentY = 0
    const allLayerGroups: any[][] = []

    for (const { element: imgElement, layers } of imageElements) {
      const imageLayerChildren: any[] = []

      // 创建单独的canvas用于当前图片
      const canvas = document.createElement('canvas')
      canvas.width = maxWidth
      canvas.height = imgElement.height
      const ctx = canvas.getContext('2d')
      if (!ctx)
        continue

      // 计算水平居中的x坐标
      const x = (maxWidth - imgElement.width) / 2
      ctx.drawImage(imgElement, x, 0)

      // 添加图片图层
      imageLayerChildren.push({
        name: imgElement.dataset.filename || `Image_${currentY}`, // 图片名称
        top: currentY,
        left: 0,
        canvas,
      })

      // 添加文字图层
      layers.filter((layer): layer is TextLayer => layer.type === 'text')
        .filter(layer => layer.content)
        .forEach((textLayer) => {
          const rgb: number[] = chroma(textLayer.color).rgb()

          imageLayerChildren.push({
            name: textLayer.content,
            text: {
              text: textLayer.content,
              transform: [
                1,
                0,
                0,
                1,
                textLayer.left + (maxWidth - imgElement.width) / 2,
                textLayer.top + textLayer.fontSize + currentY,
              ],
              antiAlias: 'smooth',
              orientation: 'horizontal',
              useFractionalGlyphWidths: true,
              style: {
                font: { name: 'MiSans-Normal' },
                fontSize: textLayer.fontSize || 30,
                fillColor: { r: rgb[0], g: rgb[1], b: rgb[2] },
              },
            },
          })
        })

      allLayerGroups.push(imageLayerChildren)
      currentY += imgElement.height
    }

    // 反转顺序并添加到PSD
    psd.children = allLayerGroups.reverse().flat()

    const buffer = writePsd(psd, { psb: totalHeight >= 30000 })
    const blob = new Blob([buffer], { type: 'application/octet-stream' })
    saveAs(blob, `${spuStore.spu}_${getCurrentImageTypeString(imagesStore.currentImageType)}.${totalHeight >= 30000 ? 'psb' : 'psd'}`)
  }
  catch (error) {
    console.error('Error creating PSD:', error)
    ElMessage.error('导出PSD失败')
  }
}

// 获取工单实例和当前节点
async function fetchCurrentNode() {
  try {
    const queryResp = await queryTicketList({
      pageNum: 1,
      pageSize: 1,
      orderBy: 'create_time desc,status asc',
      ticketDefId: 2,
      userId: userStore.userInfo?.userId,
      relevantObjIds: [spuStore.spu],
    })
    const ticketInst = queryResp.data?.records?.[0]
    if (!ticketInst)
      return

    ticketInstId.value = ticketInst.id
    const detailResp = await getDetailById(String(ticketInst.id))
    currentNodeId.value = detailResp.data.currentNodeId

    calcVisibleActions()
  }
  catch (e) {
    console.error('获取工单节点失败', e)
  }
}

async function handleWorkflowAction(action: WorkflowAction) {
  if (action === 'get_images') {
    await downloadAllPng()
    return
  }

  const mapping = actionProcessMap[action]
  if (!mapping || !ticketInstId.value)
    return

  try {
    const res = await processTicket(String(ticketInstId.value), mapping)
    if (res.success) {
      ElMessage.success(t('upload.operationSuccess'))
      await fetchCurrentNode()
    }
    else {
      ElMessage.error(t('upload.operationFailed'))
    }
  }
  catch (e) {
    console.error('工单处理失败', e)
  }
}

function calcVisibleActions() {
  const pageKey = isTranslate.value
    ? 'translate'
    : isDesign.value
      ? 'design'
      : isDesignCheck.value
        ? 'designCheck'
        : undefined

  if (!pageKey || !currentNodeId.value)
    return

  visibleActions.value = pageActionMap[pageKey][currentNodeId.value] ?? []
}

// 在页面切换或 spu 变化时重新拉取
watch(
  () => [route.path, spuStore.spu],
  () => fetchCurrentNode(),
  { immediate: true },
)

onMounted(() => {
  imagesStore.currentImageType = ImageType.MAIN
})
</script>

<template>
  <div class="image-tag-container">
    <!-- 左侧标签组 -->
    <el-radio-group :model-value="modelValue" @update:model-value="handleTagChange">
      <el-radio-button
        v-for="item in tags" :key="item.value" :value="item.value"
        :disabled="item.value === 'content' ? false : !tagCounts[item.value as keyof typeof tagCounts]"
      >
        {{ item.label }} {{ item.value === 'content' ? '' : `(${tagCounts[item.value as keyof typeof tagCounts]})` }}
      </el-radio-button>
    </el-radio-group>

    <!-- 右侧按钮组 -->
    <div class="button-group">
      <el-dropdown v-if="visibleActions.length > 0">
        <el-button type="primary">
          {{ $t('components.image_tag.operation') }}
          <el-icon class="el-icon--right">
            <ArrowDown />
          </el-icon>
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <!-- 翻译校验页面选项 -->
            <template v-if="isTranslate">
              <el-dropdown-item v-if="visibleActions.includes('get_images')" @click="downloadAllPng">
                {{ $t('components.image_tag.get_images') }}
              </el-dropdown-item>
              <el-dropdown-item v-if="visibleActions.includes('submit_verification')" @click="() => handleWorkflowAction('submit_verification')">
                {{ $t('components.image_tag.submit_verification') }}
              </el-dropdown-item>
              <el-dropdown-item v-if="visibleActions.includes('verification_approve')" @click="() => handleWorkflowAction('verification_approve')">
                {{ $t('components.image_tag.verification_approve') }}
              </el-dropdown-item>
              <el-dropdown-item v-if="visibleActions.includes('verification_reject')" @click="() => handleWorkflowAction('verification_reject')">
                {{ $t('components.image_tag.verification_reject') }}
              </el-dropdown-item>
              <el-dropdown-item v-if="visibleActions.includes('return_for_correction')" @click="() => handleWorkflowAction('return_for_correction')">
                {{ $t('components.image_tag.return_for_correction') }}
              </el-dropdown-item>
              <el-dropdown-item v-if="visibleActions.includes('corrected')" @click="() => handleWorkflowAction('corrected')">
                {{ $t('components.image_tag.corrected') }}
              </el-dropdown-item>
            </template>

            <!-- 设计页面选项 -->
            <template v-else-if="isDesign">
              <el-dropdown-item v-if="visibleActions.includes('submit_confirm')" @click="() => handleWorkflowAction('submit_confirm')">
                {{ $t('components.image_tag.submit_confirm') }}
              </el-dropdown-item>
              <el-dropdown-item v-if="visibleActions.includes('corrected')" @click="() => handleWorkflowAction('corrected')">
                {{ $t('components.image_tag.corrected') }}
              </el-dropdown-item>
            </template>

            <!-- 设计校验页面选项 -->
            <template v-else-if="isDesignCheck">
              <el-dropdown-item v-if="visibleActions.includes('submit_text_check_correct')" @click="approveDesignTextCheck">
                {{ $t('components.image_tag.submit_text_check') }}
              </el-dropdown-item>
              <el-dropdown-item v-if="visibleActions.includes('submit_text_check_reject')" @click="rejectDesignTextCheck">
                {{ $t('components.image_tag.text_check_reject') }}
              </el-dropdown-item>
              <el-dropdown-item v-if="visibleActions.includes('return_for_correction')" @click="() => handleWorkflowAction('return_for_correction')">
                {{ $t('components.image_tag.return_for_correction') }}
              </el-dropdown-item>
              <el-dropdown-item v-if="visibleActions.includes('confirmed')" @click="() => handleWorkflowAction('confirmed')">
                {{ $t('components.image_tag.confirmed') }}
              </el-dropdown-item>
            </template>

            <!-- 默认选项（上传页面等） -->
            <template v-else>
              <el-dropdown-item @click="downloadAllPng">
                {{ $t('components.image_tag.get_images') }}
              </el-dropdown-item>
            </template>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
      <el-button v-show="isDesign" :loading="imagesStore.isPolling" type="default" @click="downloadWithPsd">
        {{ $t('components.image_tag.download_with_psd') }}
      </el-button>
      <el-button v-show="isDesign" :loading="isConcatDownloading" type="default" @click="downloadConcatenatedImages">
        {{ $t('components.image_tag.download_ch') }}
      </el-button>
      <!-- <el-button v-show="isDesign" type="primary" :loading="isAllPngDownloading" @click="downloadAllPng">
        {{ $t('components.image_tag.download_all') }}
      </el-button> -->
      <el-button type="default" @click="back">
        {{ $t('components.image_tag.back_to_home') }}
      </el-button>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.image-tag-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
  background: #fff;
  border-bottom: 1px solid #e7e7e7;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

  :deep(.el-radio-group) {
    margin-left: 20px;
  }

  .button-group {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    padding: 6px 20px;

    :deep(.el-button) {
      font-size: 12px;
      // max-width: 110px;
      // height: auto !important;
      padding: 6px 12px;

      > span {
        white-space: normal;
        word-break: break-word;
        line-height: 1.2;
      }
    }

    .dropdown-icon {
      margin-right: 5px;
      font-size: 14px;
    }
  }
}

@media screen and (max-width: 768px) {
  .image-tag-container {
    flex-direction: column;
    align-items: stretch;

    :deep(.el-radio-group),
    .button-group {
      width: 100%;
      justify-content: center;
      margin-left: 0;
    }
  }
}
</style>

<script setup lang="ts">
import type { ImageInfoExtend, ImageType } from '@/api/types'
import axiosInstance from '@/api/axiosInstance'
import { retryTranslate, TranslateStatus } from '@/api/imageTranslationApi'
import { ChangeType, useImageChangeGuard } from '@/composables/useImageChangeGuard'
import { useImagesStore } from '@/stores/images'
import { Picture, Warning } from '@element-plus/icons-vue'
import { debounce } from 'lodash-es'
import { useRoute } from 'vue-router'

const props = defineProps<{
  currentType: ImageType
}>()

const emit = defineEmits<{
  (e: 'thumbnailSelected', id: string): void
  (e: 'saveLayerData'): void
}>()

const imagesStore = useImagesStore()
const currentRoute = useRoute()
const thumbnails = computed(() =>
  imagesStore.images.filter(img => img.type === props.currentType),
)
const { guardImageChange } = useImageChangeGuard(
  currentRoute.name === 'design' ? ChangeType.LAYER : ChangeType.TRANSLATION_REMARK,
)

const thumbnailUrls = ref<Record<string, string>>({})
const imageAspectRatios = ref<Record<string, number>>({})
const thumbnailsContainer = ref<HTMLElement | null>(null)
const retryLoadingMap = ref<Record<string, boolean>>({})
const thumbnailPollingMap = ref<Record<string, boolean>>({})

const saveChanges = inject('saveChanges', async () => { })
const saveDesign = inject('saveDesign', async () => { })
async function selectThumbnail(id: string) {
  const targetImage = imagesStore.images.find(img => img.id === id)

  if (!targetImage)
    return

  const shouldSave = await guardImageChange(
    () => {
      imagesStore.activeImageId = id
    },
  )
  if (shouldSave) {
    if (saveChanges) {
      await saveChanges()
    }
    if (saveDesign) {
      await saveDesign()
    }
    imagesStore.activeImageId = id
  }
}

async function loadThumbnail(id: string, src: string) {
  if (!thumbnailUrls.value[id]) {
    try {
      const response = await axiosInstance.get(src, {
        responseType: 'arraybuffer',
      })
      const buffer = response.data
      const base64String = btoa(
        new Uint8Array(buffer).reduce((data, byte) => data + String.fromCharCode(byte), ''),
      )
      thumbnailUrls.value[id] = `data:image/jpeg;base64,${base64String}`
    }
    catch (error) {
      console.error('Failed to load thumbnail:', error)
      thumbnailUrls.value[id] = '' // 使用空字符串表示加载失败
    }
  }
}

async function retryLoadThumbnail(thumbnail: ImageInfoExtend) {
  retryLoadingMap.value[thumbnail.id] = true
  thumbnailPollingMap.value[thumbnail.id] = true
  try {
    thumbnailUrls.value[thumbnail.id] = ''
    await loadThumbnail(thumbnail.id, thumbnail.image)
    await retryTranslate(thumbnail.id)
    await imagesStore.startPolling(imagesStore.getCurrentTransId, props.currentType)
    // 更新缩略图状态
    thumbnailPollingMap.value[thumbnail.id] = thumbnail.status === TranslateStatus.PROCESSING
  }
  finally {
    retryLoadingMap.value[thumbnail.id] = false
  }
}

function onImageLoad(event: Event, id: string) {
  const img = event.target as HTMLImageElement
  const aspectRatio = img.naturalWidth / img.naturalHeight
  imageAspectRatios.value[id] = aspectRatio
}

// 处理图片加载错误
function handleImageError(e: Event) {
  // 当图片加载失败时，将图片替换为默认图标
  const imgElement = e.target as HTMLImageElement
  imgElement.style.display = 'none'
  // 显示父元素中的图标
  const iconElement = imgElement.parentElement?.querySelector('.design-icon') as HTMLElement
  if (iconElement) {
    iconElement.style.display = 'flex'
  }
}

// 监听缩略图列表变化，自动加载新的缩略图
watch(thumbnails, (newThumbnails) => {
  newThumbnails.forEach((thumbnail) => {
    if (thumbnail && !thumbnailUrls.value[thumbnail.id]) {
      loadThumbnail(thumbnail.id, thumbnail.thumbnail)
    }
  })
}, { immediate: true })

// 计算缩略图的状态
const getThumbnailState = computed(() => (thumbnail: ImageInfoExtend) => {
  // 重试加载状态
  if (retryLoadingMap.value[thumbnail.id]) {
    return 'retrying'
  }
  // 解析失败状态
  if (imagesStore.isThumbnailFailed(thumbnail.id)) {
    return thumbnail.status === TranslateStatus.INVALID_SIZE ? 'invalid-size' : 'failed'
  }
  // 轮询中状态
  if (thumbnailPollingMap.value[thumbnail.id] || thumbnail.status !== TranslateStatus.SUCCESS) {
    return 'loading'
  }
  return 'ready'
})

const debouncedScrollToThumbnail = debounce((thumbnail: HTMLElement) => {
  thumbnailsContainer.value?.scrollTo({
    top: thumbnail.offsetTop - (thumbnailsContainer.value as HTMLElement).offsetHeight / 2 + thumbnail.offsetHeight / 2,
    behavior: 'smooth',
  })
}, 200)

// 监听activeImageId变化，通知父组件
watch(() => imagesStore.activeImageId, (newId) => {
  if (newId && thumbnails.value.some(t => t.id === newId)) {
    emit('thumbnailSelected', newId)
    nextTick(() => {
      const selectedThumbnail = thumbnailsContainer.value?.querySelector(`.thumbnail[data-id="${newId}"]`)
      if (selectedThumbnail) {
        const containerRect = thumbnailsContainer.value?.getBoundingClientRect()
        const thumbnailRect = selectedThumbnail.getBoundingClientRect()

        if (!containerRect)
          return

        const isInView = (
          thumbnailRect.top >= containerRect.top
          && thumbnailRect.bottom <= containerRect.bottom
        )

        if (!isInView) {
          debouncedScrollToThumbnail(selectedThumbnail as HTMLElement)
        }
      }
    })
  }
}, { immediate: true })
// 添加自动选择第一张图片的逻辑
onMounted(() => {
  if (thumbnails.value.length > 0 && (imagesStore.activeImageId === '0' || !imagesStore.activeImageId)) {
    const firstImage = thumbnails.value[0]
    selectThumbnail(firstImage.id)
  }
})
</script>

<template>
  <div ref="thumbnailsContainer" class="thumbnail-list">
    <div v-if="thumbnails.length === 0" class="empty-state">
      暂无图片
    </div>
    <div
      v-for="(thumbnail, index) in thumbnails" :key="thumbnail.id" class="thumbnail" :class="{
        'selected': thumbnail.id === imagesStore.activeImageId,
        'has-remark': currentRoute.path === '/design' && thumbnail.remark,
      }" :data-id="thumbnail.id" @click="selectThumbnail(thumbnail.id)"
    >
      <div class="design-info-preview">
        <div class="design-image-container">
          <img
            v-if="thumbnailUrls[thumbnail.id]" :src="thumbnailUrls[thumbnail.id]" class="design-image"
            @load="onImageLoad($event, thumbnail.id)"
            @error="handleImageError($event)"
          >
          <el-icon v-else class="design-icon">
            <Picture />
          </el-icon>

          <!-- 图片序号 -->
          <div class="design-number">
            <span>{{ index + 1 }}</span>
          </div>
        </div>
      </div>

      <el-tooltip
        :content="thumbnail.error"
        placement="right"
        :disabled="getThumbnailState(thumbnail) !== 'invalid-size'"
      >
        <div v-show="getThumbnailState(thumbnail) !== 'ready'" class="loading-overlay">
          <div class="loading-spinner" />
          <div v-show="getThumbnailState(thumbnail) === 'failed'" class="error-message">
            解析失败
          </div>
          <div v-show="getThumbnailState(thumbnail) === 'invalid-size'" class="error-message__icon">
            <el-icon><Warning /></el-icon>
          </div>
          <el-button
            v-show="getThumbnailState(thumbnail) === 'failed'" class="retry-button"
            :loading="retryLoadingMap[thumbnail.id]" @click="retryLoadThumbnail(thumbnail)"
          >
            重试
          </el-button>
        </div>
      </el-tooltip>

      <!-- 备注标记 -->
      <div v-if="currentRoute.path === '/design' && thumbnail.remark" class="remark-indicator">
        <el-tooltip content="有设计校验标记" placement="top">
          <el-icon><Warning /></el-icon>
        </el-tooltip>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.thumbnail-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  justify-content: flex-start;
  max-height: 100%;
  overflow-y: auto;
  min-height: 100px;
  background-color: var(--el-bg-color);

  /* 自定义滚动条样式 */
  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background-color: var(--el-border-color-light);
    border-radius: 2px;

    &:hover {
      background-color: var(--el-border-color);
    }
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 120px;
  color: var(--el-text-color-secondary);
  font-size: 14px;

  &::before {
    content: '';
    display: block;
    width: 40px;
    height: 40px;
    margin-bottom: 12px;
    background-color: var(--el-fill-color-light);
    mask-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-1 16H6c-.55 0-1-.45-1-1V6c0-.55.45-1 1-1h12c.55 0 1 .45 1 1v12c0 .55-.45 1-1 1zm-4.44-6.44l-2.35 3.02-1.56-1.88c-.2-.25-.58-.24-.78.01l-1.74 2.23c-.2.25-.2.61 0 .86.2.25.58.26.78.01l1.35-1.73 1.58 1.9c.2.25.58.24.78-.01l2.55-3.27c.2-.25.19-.61-.02-.86-.21-.25-.59-.25-.79 0z"/></svg>');
    mask-repeat: no-repeat;
    mask-position: center;
    mask-size: contain;
    -webkit-mask-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-1 16H6c-.55 0-1-.45-1-1V6c0-.55.45-1 1-1h12c.55 0 1 .45 1 1v12c0 .55-.45 1-1 1zm-4.44-6.44l-2.35 3.02-1.56-1.88c-.2-.25-.58-.24-.78.01l-1.74 2.23c-.2.25-.2.61 0 .86.2.25.58.26.78.01l1.35-1.73 1.58 1.9c.2.25.58.24.78-.01l2.55-3.27c.2-.25.19-.61-.02-.86-.21-.25-.59-.25-.79 0z"/></svg>');
    -webkit-mask-repeat: no-repeat;
    -webkit-mask-position: center;
    -webkit-mask-size: contain;
  }
}

.thumbnail {
  position: relative;
  aspect-ratio: 1;
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 4px;
  overflow: hidden;
  cursor: pointer;
  background-color: var(--el-fill-color-blank);
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.2s ease;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.02);
  width: 100%;

  &:hover {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.03);
    border-color: var(--el-border-color);
  }

  &.selected {
    border-color: var(--el-color-primary);
    box-shadow: 0 0 0 1px var(--el-color-primary-light-8);
  }

  &.has-remark {
    border-color: var(--el-color-danger-light-8);
  }

  &.selected.has-remark {
    border-color: var(--el-color-danger);
    box-shadow: 0 0 0 1px var(--el-color-danger-light-8);
  }
}

.design-info-preview {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
}

.design-icon {
  font-size: 24px;
  color: var(--el-color-primary-light-5);
  display: flex;
}

.design-image-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  background-color: var(--el-fill-color-light);
  position: relative;
  padding: 4px;

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom, rgba(0, 0, 0, 0.02) 0%, rgba(0, 0, 0, 0) 100%);
    pointer-events: none;
  }
}

.design-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.design-number {
  position: absolute;
  bottom: 4px;
  left: 4px;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 2px;
  z-index: 1;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
  z-index: 2;

  .error-message {
    color: #fff;
    font-size: 12px;
    position: absolute;
    top: 8px;
    left: 8px;
    background-color: rgba(244, 67, 54, 0.9);
    padding: 2px 8px;
    border-radius: 4px;
    backdrop-filter: blur(4px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }

  .error-message__icon {
    @extend .error-message;
    color: #ff0000;
    font-size: 24px;
    padding: 0;
    background-color: transparent;
    backdrop-filter: none;
    box-shadow: none;
  }

  .retry-button {
    font-size: 13px;
    font-weight: 500;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }
}

.loading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid rgba(24, 144, 255, 0.1);
  border-left: 2px solid #1890ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.remark-indicator {
  position: absolute;
  top: 4px;
  right: 4px;
  background-color: var(--el-color-danger);
  color: white;
  width: 14px;
  height: 14px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 10px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  z-index: 1;

  .el-icon {
    font-size: 10px;
    transform: scale(0.7);
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
</style>

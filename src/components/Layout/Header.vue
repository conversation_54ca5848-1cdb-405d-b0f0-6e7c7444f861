<script setup lang="ts">
import { logout } from '@/api/auth'
import { useLangStore } from '@/stores/lang'
import { useToolStore } from '@/stores/tool'
import { useUnsavedChangesStore } from '@/stores/unsavedChanges'
import { useUserStore } from '@/stores/user'
import { useRoute, useRouter } from 'vue-router'

const router = useRouter()
const route = useRoute()
const langStore = useLangStore()
const userStore = useUserStore()
const toolStore = useToolStore()
const currentLang = ref(langStore.currentLang)
const unsavedChangesStore = useUnsavedChangesStore()
const currentPath = computed(() => route.path)

async function navigateTo(path: string) {
  if (unsavedChangesStore.isSaveDialogOpen) {
    return
  }
  unsavedChangesStore.hasUnsavedChanges = false
  const query: { [key: string]: string } = {}
  if (route.query.transId) {
    const transId = Array.isArray(route.query.transId) ? route.query.transId[0] : route.query.transId
    query.transId = transId?.toString() ?? ''
  }
  if (route.query.spu) {
    const spu = Array.isArray(route.query.spu) ? route.query.spu[0] : route.query.spu
    query.spu = spu?.toString() ?? ''
  }
  router.push({ path, query })
}

function toggleLang() {
  const newLang = currentLang.value === 'zh' ? 'ja' : 'zh'
  currentLang.value = newLang
  langStore.changeLang(newLang)
}

async function handleLogout() {
  await logout(userStore.userInfo?.jwt).finally(() => {
    userStore.logout()
    router.push('/login')
  })
}

const isShowCenter = computed(() => {
  return currentPath.value !== '/login' && currentPath.value !== '/upload'
})
</script>

<template>
  <div class="header">
    <div class="header__left">
      <h1>CAGUUU</h1>
      <template v-if="currentPath === '/login'">
        <el-radio-group v-model="toolStore.selectedTool" size="small" class="tool-select">
          <el-radio-button label="localization">
            {{ $t('page.common.product_localization_tool') }}
          </el-radio-button>
          <el-radio-button label="freight">
            {{ $t('page.common.freight_estimation_tool') }}
          </el-radio-button>
          <el-radio-button label="warehouse">
            {{ $t('page.common.warehouse_testing_tool') }}
          </el-radio-button>
        </el-radio-group>
      </template>
      <span v-else>{{ $t('page.common.product_localization_tool') }}</span>
    </div>
    <div v-show="isShowCenter" class="header__center">
      <div
        v-for="tag in userStore.accessiblePages"
        :key="tag.path"
        class="header__center-tag" :class="[{ active: currentPath === tag.path }]"
        @click="navigateTo(tag.path)"
      >
        <span>{{ $t(`page.titles.${tag.key}`) }}</span>
      </div>
    </div>
    <div class="header__right">
      <el-tooltip :content="$t('page.login.logout')" placement="bottom">
        <i v-show="currentPath !== '/login'" class="iconfont icon-logout" @click="handleLogout" />
      </el-tooltip>
      <el-tooltip :content="$t('page.common.change_language')" placement="bottom">
        <i class="iconfont icon-riyu2-1" @click="toggleLang" />
      </el-tooltip>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 100%;

  &__left {
    display: flex;
    align-items: center;

    h1 {
      margin: 0;
      font-size: 20px;
      font-weight: bold;
      color: #399e96;
      font-family: 'Arial Negreta', 'Arial Normal', 'Arial', sans-serif;
    }

    span {
      margin-left: 10px;
      font-size: 14px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans',
        sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
      font-weight: 600;
      line-height: 18px;
      color: #666;
    }

    .tool-select {
      margin-left: 14px;
    }
  }

  &__center {
    display: flex;
    gap: 10px;

    .header__center-tag {
      padding: 8px 16px;
      background-color: #f5f7fa;
      border-radius: 3px;
      cursor: pointer;
      transition: all 0.3s ease;
      min-width: 96px;
      text-align: center;

      &:hover {
        background-color: #e6f3f2;
        transform: translateY(-1px);
      }

      &.active {
        background-color: #399e96;

        span {
          color: white;
        }

        &:hover {
          background-color: #2e8076;
        }
      }
    }
    span {
      font-size: 14px;
      color: #666;
      font-weight: 500;
    }
  }

  &__right {
    display: flex;
    align-items: center;
    gap: 16px;

    .iconfont {
      cursor: pointer;
      font-size: 24px;
      color: #399e96;
      transition: color 0.3s;

      &:hover {
        color: #2e8076;
      }

      &.active {
        color: #2e8076;
      }
    }

    .icon-logout {
      font-size: 20px;
    }
  }
}
</style>

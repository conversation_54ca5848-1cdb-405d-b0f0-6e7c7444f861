.cg-layout {
  width: 100%;
  height: 100vh;
  min-width: 1200px;
  display: flex;
  flex-direction: column;
  position: relative;
  box-sizing: border-box;
  background-color: #ffffff;

  &__top {
    height: 50px;
    background-color: #fff;
    border-bottom: 1px solid #e7e7e7;
    position: sticky;
    top: 0;
    z-index: 100;
    display: flex;
    align-items: center;
    padding: 0 20px;
  }

  &__main {
    flex: 1;
    overflow-y: auto;
    height: calc(100vh - 50px);
  }

  @media screen and (max-width: 1200px) {
    min-width: 100%;
  }
}

<script setup lang="ts">
import type { UploadInstance, UploadProps, UploadRawFile, UploadUserFile } from 'element-plus'
import { uploadDesign } from '@/api/imageTranslationApi'
import { ImageType } from '@/api/types'
import { useImagesStore } from '@/stores/images'
import { Delete, Plus, RefreshRight, ZoomIn } from '@element-plus/icons-vue'
import { genFileId } from 'element-plus'
import { storeToRefs } from 'pinia'
import { useI18n } from 'vue-i18n'

defineProps<{
  visible: boolean
}>()

const emit = defineEmits<{
  (e: 'uploadSuccess'): void
  (e: 'update:visible', value: boolean): void
}>()

const { t } = useI18n()
const imagesStore = useImagesStore()
const { transId } = storeToRefs(imagesStore)

const activeTab = ref('SKU')

const skuFileList = ref<UploadUserFile[]>([])
const detailFileList = ref<UploadUserFile[]>([])
const mainFileList = ref<UploadUserFile[]>([])
const installationFileList = ref<UploadUserFile[]>([])

const uploading = ref(false)

// 图片预览相关
const dialogImageUrl = ref('')
const dialogVisible = ref(false)

// 处理图片预览
function handlePreview(file: UploadUserFile) {
  dialogImageUrl.value = file.url || URL.createObjectURL(file.raw as Blob)
  dialogVisible.value = true
}

// 处理替换图片
function handleReplace(file: UploadUserFile, uploadRef: UploadInstance | undefined) {
  if (!uploadRef)
    return

  // 创建一个文件选择器
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = 'image/jpeg,image/png,image/gif,image/webp'
  input.style.display = 'none'
  document.body.appendChild(input)

  // 监听文件选择事件
  input.onchange = (e) => {
    const target = e.target as HTMLInputElement
    if (target.files && target.files.length > 0) {
      const newFile = target.files[0]

      // 找到当前文件在文件列表中的索引
      const fileList = getCurrentFileList()
      const index = fileList.value.findIndex((f: UploadUserFile) => f.uid === file.uid)

      if (index !== -1) {
        // 创建新的上传文件对象
        const rawFile = newFile as UploadRawFile
        rawFile.uid = genFileId()

        // 替换文件列表中的文件
        const newUploadFile: UploadUserFile = {
          name: newFile.name,
          size: newFile.size,
          uid: rawFile.uid,
          url: URL.createObjectURL(newFile),
          raw: rawFile,
        }

        // 删除旧文件并添加新文件
        fileList.value.splice(index, 1, newUploadFile)
      }
    }

    // 清理文件选择器
    document.body.removeChild(input)
  }

  // 触发文件选择器
  input.click()
}

// 获取当前标签页的文件列表引用
function getCurrentFileList() {
  switch (activeTab.value) {
    case 'SKU':
      return skuFileList
    case 'DETAIL':
      return detailFileList
    case 'MAIN':
      return mainFileList
    case 'INSTALLATION':
      return installationFileList
    default:
      return skuFileList
  }
}

// 每种类型的文件列表引用
const skuUploadRef = ref<UploadInstance>()
const detailUploadRef = ref<UploadInstance>()
const mainUploadRef = ref<UploadInstance>()
const installationUploadRef = ref<UploadInstance>()

function getCurrentUploadRef() {
  switch (activeTab.value) {
    case 'SKU':
      return skuUploadRef
    case 'DETAIL':
      return detailUploadRef
    case 'MAIN':
      return mainUploadRef
    case 'INSTALLATION':
      return installationUploadRef
    default:
      return skuUploadRef
  }
}

const beforeUpload: UploadProps['beforeUpload'] = (file) => {
  const isImage = /\.(jpeg|jpg|png|gif|webp)$/i.test(file.name)
  if (!isImage) {
    ElMessage.error(t('upload.uploadImageOnly'))
    return false
  }

  const maxSize = 10 * 1024 * 1024 // 10MB
  if (file.size > maxSize) {
    ElMessage.error(t('upload.imageSizeExceed'))
    return false
  }

  return true
}

const handleExceed: UploadProps['onExceed'] = (files) => {
  const uploadRef = getCurrentUploadRef().value
  if (!uploadRef)
    return

  for (const file of files) {
    const rawFile = file as UploadRawFile
    rawFile.uid = genFileId()
    uploadRef.handleStart(rawFile)
  }
}

async function submitUpload() {
  if (!transId.value) {
    ElMessage.warning(t('upload.noTransId'))
    return
  }

  // 检查是否有文件要上传
  const hasFiles = skuFileList.value.length > 0
    || detailFileList.value.length > 0
    || mainFileList.value.length > 0
    || installationFileList.value.length > 0

  if (!hasFiles) {
    ElMessage.warning(t('upload.pleaseSelectFile'))
    return
  }

  uploading.value = true
  try {
    // 上传SKU图片
    if (skuFileList.value.length > 0) {
      const files = skuFileList.value.map(file => file.raw as File)
      await uploadDesign(transId.value, ImageType.SKU, files)
    }

    // 上传详情图片
    if (detailFileList.value.length > 0) {
      const files = detailFileList.value.map(file => file.raw as File)
      await uploadDesign(transId.value, ImageType.DETAIL, files)
    }

    // 上传主图
    if (mainFileList.value.length > 0) {
      const files = mainFileList.value.map(file => file.raw as File)
      await uploadDesign(transId.value, ImageType.MAIN, files)
    }

    // 上传安装说明图片
    if (installationFileList.value.length > 0) {
      const files = installationFileList.value.map(file => file.raw as File)
      await uploadDesign(transId.value, ImageType.INSTALLATION, files)
    }

    ElMessage.success(t('upload.uploadSuccess'))
    emit('uploadSuccess')
    emit('update:visible', false)
  }
  catch (error) {
    console.error('Upload design error:', error)
    ElMessage.error(t('upload.uploadFailed'))
  }
  finally {
    uploading.value = false
  }
}
</script>

<template>
  <el-dialog
    :model-value="visible"
    :title="t('upload.uploadDesign')"
    width="800px"
    destroy-on-close
    @update:model-value="(val: boolean) => emit('update:visible', val)"
  >
    <div class="design-upload-form">
      <!-- 标签页选择不同类型的图片 -->
      <el-tabs v-model="activeTab" class="upload-tabs">
        <el-tab-pane :label="t('components.image_tag.sku')" name="SKU">
          <el-upload
            ref="skuUploadRef"
            v-model:file-list="skuFileList"
            class="upload-wall"
            :auto-upload="false"
            :before-upload="beforeUpload"
            accept="image/jpeg,image/png,image/gif,image/webp"
            :multiple="true"
            :limit="20"
            :on-exceed="handleExceed"
            :on-preview="handlePreview"
            list-type="picture-card"
          >
            <template #file="{ file }">
              <div class="upload-file-item">
                <img class="upload-file-img" :src="file.url" alt="">
                <div class="upload-file-actions">
                  <span class="upload-file-action" @click.stop="handlePreview(file)">
                    <el-icon><ZoomIn /></el-icon>
                  </span>
                  <span class="upload-file-action" @click.stop="handleReplace(file, skuUploadRef)">
                    <el-icon><RefreshRight /></el-icon>
                  </span>
                  <span class="upload-file-action" @click.stop="skuUploadRef?.handleRemove(file)">
                    <el-icon><Delete /></el-icon>
                  </span>
                </div>
                <div class="upload-file-name">
                  {{ file.name }}
                </div>
              </div>
            </template>
            <el-icon><Plus /></el-icon>
          </el-upload>
        </el-tab-pane>

        <el-tab-pane :label="t('components.image_tag.detail')" name="DETAIL">
          <el-upload
            ref="detailUploadRef"
            v-model:file-list="detailFileList"
            class="upload-wall"
            :auto-upload="false"
            :before-upload="beforeUpload"
            accept="image/jpeg,image/png,image/gif,image/webp"
            :multiple="true"
            :limit="20"
            :on-exceed="handleExceed"
            :on-preview="handlePreview"
            list-type="picture-card"
          >
            <template #file="{ file }">
              <div class="upload-file-item">
                <img class="upload-file-img" :src="file.url" alt="">
                <div class="upload-file-actions">
                  <span class="upload-file-action" @click.stop="handlePreview(file)">
                    <el-icon><ZoomIn /></el-icon>
                  </span>
                  <span class="upload-file-action" @click.stop="handleReplace(file, detailUploadRef)">
                    <el-icon><RefreshRight /></el-icon>
                  </span>
                  <span class="upload-file-action" @click.stop="detailUploadRef?.handleRemove(file)">
                    <el-icon><Delete /></el-icon>
                  </span>
                </div>
                <div class="upload-file-name">
                  {{ file.name }}
                </div>
              </div>
            </template>
            <el-icon><Plus /></el-icon>
          </el-upload>
        </el-tab-pane>

        <el-tab-pane :label="t('components.image_tag.main_image')" name="MAIN">
          <el-upload
            ref="mainUploadRef"
            v-model:file-list="mainFileList"
            class="upload-wall"
            :auto-upload="false"
            :before-upload="beforeUpload"
            accept="image/jpeg,image/png,image/gif,image/webp"
            :multiple="true"
            :limit="20"
            :on-exceed="handleExceed"
            :on-preview="handlePreview"
            list-type="picture-card"
          >
            <template #file="{ file }">
              <div class="upload-file-item">
                <img class="upload-file-img" :src="file.url" alt="">
                <div class="upload-file-actions">
                  <span class="upload-file-action" @click.stop="handlePreview(file)">
                    <el-icon><ZoomIn /></el-icon>
                  </span>
                  <span class="upload-file-action" @click.stop="handleReplace(file, mainUploadRef)">
                    <el-icon><RefreshRight /></el-icon>
                  </span>
                  <span class="upload-file-action" @click.stop="mainUploadRef?.handleRemove(file)">
                    <el-icon><Delete /></el-icon>
                  </span>
                </div>
                <div class="upload-file-name">
                  {{ file.name }}
                </div>
              </div>
            </template>
            <el-icon><Plus /></el-icon>
          </el-upload>
        </el-tab-pane>

        <el-tab-pane :label="t('components.image_tag.installation')" name="INSTALLATION">
          <el-upload
            ref="installationUploadRef"
            v-model:file-list="installationFileList"
            class="upload-wall"
            :auto-upload="false"
            :before-upload="beforeUpload"
            accept="image/jpeg,image/png,image/gif,image/webp"
            :multiple="true"
            :limit="20"
            :on-exceed="handleExceed"
            :on-preview="handlePreview"
            list-type="picture-card"
          >
            <template #file="{ file }">
              <div class="upload-file-item">
                <img class="upload-file-img" :src="file.url" alt="">
                <div class="upload-file-actions">
                  <span class="upload-file-action" @click.stop="handlePreview(file)">
                    <el-icon><ZoomIn /></el-icon>
                  </span>
                  <span class="upload-file-action" @click.stop="handleReplace(file, installationUploadRef)">
                    <el-icon><RefreshRight /></el-icon>
                  </span>
                  <span class="upload-file-action" @click.stop="installationUploadRef?.handleRemove(file)">
                    <el-icon><Delete /></el-icon>
                  </span>
                </div>
                <div class="upload-file-name">
                  {{ file.name }}
                </div>
              </div>
            </template>
            <el-icon><Plus /></el-icon>
          </el-upload>
        </el-tab-pane>
      </el-tabs>
    </div>

    <template #footer>
      <el-button @click="emit('update:visible', false)">
        {{ t('dialog.ai_translate.cancel') }}
      </el-button>
      <el-button type="primary" :loading="uploading" @click="submitUpload">
        {{ t('upload.startUpload') }}
      </el-button>
    </template>
  </el-dialog>

  <!-- 图片预览对话框 -->
  <el-dialog
    v-model="dialogVisible"
    :title="t('upload.imagePreview')"
    width="60%"
    center
    append-to-body
    destroy-on-close
    :close-on-click-modal="true"
    :show-close="true"
    class="image-preview-dialog"
  >
    <div class="preview-container">
      <img
        :src="dialogImageUrl"
        alt="Preview Image"
        class="preview-image"
        @click="dialogVisible = false"
      >
    </div>
  </el-dialog>
</template>

<style lang="scss" scoped>
.design-upload-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.upload-tabs {
  width: 100%;
}

.upload-wall {
  :deep(.el-upload--picture-card) {
    --el-upload-picture-card-size: 120px;
    margin-right: 8px;
    margin-bottom: 8px;
  }

  :deep(.el-upload-list--picture-card) {
    --el-upload-list-picture-card-size: 120px;
    .el-upload-list__item {
      margin: 0 8px 8px 0;
    }
  }
}
</style>

<style lang="scss">
.image-preview-dialog {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.preview-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background-color: rgba(0, 0, 0, 0.02);
  border-radius: 4px;
  padding: 10px;
}

.preview-image {
  max-width: 100%;
  max-height: 70vh;
  object-fit: contain;
  cursor: pointer;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transition: transform 0.3s;
}

:deep(.image-preview-dialog) {
  .el-dialog__header {
    padding: 15px 20px;
    margin-right: 0;
    border-bottom: 1px solid #ebeef5;
  }

  .el-dialog__body {
    padding: 20px;
  }

  .el-dialog__headerbtn {
    top: 15px;
  }
}

/* 自定义上传文件项样式 */
.upload-file-item {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

.upload-file-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.upload-file-actions {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  background-color: rgba(0, 0, 0, 0.5);
  opacity: 0;
  transition: opacity 0.3s;
}

.upload-file-item:hover .upload-file-actions {
  opacity: 1;
}

.upload-file-action {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.8);
  color: #606266;
  cursor: pointer;
  transition: all 0.3s;
}

.upload-file-action:hover {
  background-color: #fff;
  color: #409eff;
  transform: scale(1.1);
}

.upload-file-name {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 4px;
  font-size: 12px;
  color: #fff;
  background-color: rgba(0, 0, 0, 0.5);
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>

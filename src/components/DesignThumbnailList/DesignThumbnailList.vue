<script setup lang="ts">
import type { ImageType } from '@/api/types'
import { useDesignStore } from '@/stores/design'
import { useImagesStore } from '@/stores/images'
import { Picture, Warning } from '@element-plus/icons-vue'

const props = defineProps<{
  currentType: ImageType
}>()

const emit = defineEmits<{
  (e: 'thumbnailSelected', id: string): void
}>()

const imagesStore = useImagesStore()
const designStore = useDesignStore()
const thumbnailsContainer = ref<HTMLElement | null>(null)

// 选择设计稿
async function selectDesign(designId: string) {
  if (designId) {
    emit('thumbnailSelected', designId)
  }
}

// 自动选择第一个设计稿
function trySelectFirstDesign() {
  const designs = designStore.getDesignInfoByType(props.currentType).value
  if (designs.length > 0) {
    const firstDesign = designs[0]
    if (firstDesign.designId) {
      // 选择第一个设计稿
      selectDesign(firstDesign.designId)
    }
  }
}

// 监听设计稿数据加载状态
watch(
  () => designStore.getDesignInfoByType(props.currentType).value,
  (newDesigns) => {
    imagesStore.activeImageId = '0'
    // 设计稿数据变化时重置激活图片ID

    if (newDesigns.length > 0) {
      // 尝试选择第一个设计稿
      trySelectFirstDesign()
    }
  },
)

// 监听加载状态
watch(
  () => designStore.getLoadingStateByType(props.currentType).value,
  (isLoading) => {
    if (!isLoading) {
      // 数据加载完成后尝试选择第一个设计稿
      nextTick(() => {
        trySelectFirstDesign()
      })
    }
  },
)

onMounted(() => {
  // 组件挂载时尝试选择第一个设计稿
  trySelectFirstDesign()
})

// No need for scroll functionality in this component

// 处理图片加载错误
function handleImageError(e: Event) {
  // 当图片加载失败时，将图片替换为默认图标
  const imgElement = e.target as HTMLImageElement
  imgElement.style.display = 'none'
  // 显示父元素中的图标
  const iconElement = imgElement.parentElement?.querySelector('.design-icon') as HTMLElement
  if (iconElement) {
    iconElement.style.display = 'flex'
  }
}
</script>

<template>
  <div ref="thumbnailsContainer" v-loading="designStore.getLoadingStateByType(props.currentType).value" class="thumbnail-list">
    <div v-if="designStore.getDesignInfoByType(props.currentType).value.length === 0 && !designStore.getLoadingStateByType(props.currentType).value" class="empty-state">
      暂无设计稿
    </div>
    <div
      v-for="(design, index) in designStore.getDesignInfoByType(props.currentType).value"
      :key="design.designId || index"
      class="thumbnail"
      :class="{
        'selected': design.designId === imagesStore.activeImageId,
        'has-remark': design.remark,
      }"

      :data-id="design.designId"
      @click="selectDesign(design.designId || '')"
    >
      <!-- 设计稿信息 -->
      <div class="design-info-preview">
        <!-- 设计稿图片 -->
        <div class="design-image-container">
          <img
            v-if="design.designLink"
            :src="design.designLink"
            class="design-image"
            alt="设计稿图片"
            crossorigin="anonymous"
            @error="handleImageError"
          >
          <el-icon v-else class="design-icon">
            <Picture />
          </el-icon>

          <!-- 设计稿序号 -->
          <div class="design-number">
            <span>{{ index + 1 }}</span>
          </div>
        </div>
      </div>

      <!-- 备注标记 -->
      <div v-if="design.remark" class="remark-indicator">
        <el-tooltip content="有设计校验标记" placement="top">
          <el-icon><Warning /></el-icon>
        </el-tooltip>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.thumbnail-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  justify-content: flex-start;
  max-height: 100%;
  overflow-y: auto;
  min-height: 100px;
  background-color: var(--el-bg-color);

  /* 自定义滚动条样式 */
  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background-color: var(--el-border-color-light);
    border-radius: 2px;

    &:hover {
      background-color: var(--el-border-color);
    }
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 120px;
  color: var(--el-text-color-secondary);
  font-size: 14px;

  &::before {
    content: '';
    display: block;
    width: 40px;
    height: 40px;
    margin-bottom: 12px;
    background-color: var(--el-fill-color-light);
    mask-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-1 16H6c-.55 0-1-.45-1-1V6c0-.55.45-1 1-1h12c.55 0 1 .45 1 1v12c0 .55-.45 1-1 1zm-4.44-6.44l-2.35 3.02-1.56-1.88c-.2-.25-.58-.24-.78.01l-1.74 2.23c-.2.25-.2.61 0 .86.2.25.58.26.78.01l1.35-1.73 1.58 1.9c.2.25.58.24.78-.01l2.55-3.27c.2-.25.19-.61-.02-.86-.21-.25-.59-.25-.79 0z"/></svg>');
    mask-repeat: no-repeat;
    mask-position: center;
    mask-size: contain;
    -webkit-mask-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-1 16H6c-.55 0-1-.45-1-1V6c0-.55.45-1 1-1h12c.55 0 1 .45 1 1v12c0 .55-.45 1-1 1zm-4.44-6.44l-2.35 3.02-1.56-1.88c-.2-.25-.58-.24-.78.01l-1.74 2.23c-.2.25-.2.61 0 .86.2.25.58.26.78.01l1.35-1.73 1.58 1.9c.2.25.58.24.78-.01l2.55-3.27c.2-.25.19-.61-.02-.86-.21-.25-.59-.25-.79 0z"/></svg>');
    -webkit-mask-repeat: no-repeat;
    -webkit-mask-position: center;
    -webkit-mask-size: contain;
  }
}

.thumbnail {
  position: relative;
  aspect-ratio: 1;
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 4px;
  overflow: hidden;
  cursor: pointer;
  background-color: var(--el-fill-color-blank);
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.2s ease;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.02);
  width: 100%;

  &:hover {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.03);
    border-color: var(--el-border-color);
  }

  &.selected {
    border-color: var(--el-color-primary);
    box-shadow: 0 0 0 1px var(--el-color-primary-light-8);
  }

  &.has-remark {
    border-color: var(--el-color-danger-light-8);
  }

  &.selected.has-remark {
    border-color: var(--el-color-danger);
    box-shadow: 0 0 0 1px var(--el-color-danger-light-8);
  }
}

.design-info-preview {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
}

.design-icon {
  font-size: 24px;
  color: var(--el-color-primary-light-5);
  display: flex;
}

.design-image-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  background-color: var(--el-fill-color-light);
  position: relative;
  padding: 4px;

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom, rgba(0, 0, 0, 0.02) 0%, rgba(0, 0, 0, 0) 100%);
    pointer-events: none;
  }
}

.design-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.design-number {
  position: absolute;
  bottom: 4px;
  left: 4px;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 2px;
  z-index: 1;
}

.remark-indicator {
  position: absolute;
  top: 4px;
  right: 4px;
  background-color: var(--el-color-danger);
  color: white;
  width: 14px;
  height: 14px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 10px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  z-index: 1;

  .el-icon {
    font-size: 10px;
    transform: scale(0.7);
  }
}
</style>

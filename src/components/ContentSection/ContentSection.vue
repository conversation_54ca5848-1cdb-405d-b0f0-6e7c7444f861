<script setup lang="ts">
import type { CopywritingInfoVO, CopywritingVO, SaveCopywritingReq, SkuCopywritingInfo } from '@/api/types'
import { queryCopywriting, saveCopywriting } from '@/api/imageTranslationApi'
import AITranslateDialog from '@/components/AITranslateDialog/AITranslateDialog.vue'
import { Picture } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { computed, onMounted, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRoute } from 'vue-router'

const { t } = useI18n()
const route = useRoute()

// 判断当前页面
const isTranslatePage = computed(() => route.path === '/translate')
const isDesignPage = computed(() => route.path === '/design')
const isDesignCheckPage = computed(() => route.path === '/design-check')

// 加载状态
const loading = ref(false)

// 商品信息
const productInfo = ref({
  chineseName: '',
  japaneseName: '',
  link: '',
  nameResourceId: '', // 用于保存时的资源ID
})

// 表格数据
const tableData = ref<{
  id: string
  image: string
  thumbnail: string
  items: Array<{
    label: string
    chinese: string
    japanese: string
    resourceId?: string
    remark?: string
  }>
}[]>([])

// 备注内容
const remark = ref('')

// 获取当前翻译ID
function getCurrentTransId() {
  const transId = route.query.transId as string
  return transId || ''
}

// 清空内容
function clearContent() {
  // 清空日文名称输入框
  productInfo.value.japaneseName = ''
  // 清空备注
  remark.value = ''
}

// AI翻译对话框相关状态
const showAIDialog = ref(false)
const currentTranslateText = ref('')

// 打开AI翻译对话框
function aiTranslate() {
  // 设置要翻译的内容为商品中文名
  currentTranslateText.value = productInfo.value.chineseName
  showAIDialog.value = true
}

// 处理AI翻译结果
function handleTranslateResult(result: string) {
  if (result) {
    productInfo.value.japaneseName = result
  }
  showAIDialog.value = false
}

// 保存文案信息
async function saveCopywritingData() {
  try {
    const saveRequests: SaveCopywritingReq[] = []

    // 添加商品名称的保存请求
    if (productInfo.value.nameResourceId) {
      saveRequests.push({
        resourceId: productInfo.value.nameResourceId,
        transValue: productInfo.value.japaneseName,
        transRemark: remark.value,
      })
    }

    // 添加SKU信息的保存请求
    tableData.value.forEach((skuItem) => {
      skuItem.items.forEach((item) => {
        if (item.resourceId) {
          saveRequests.push({
            resourceId: item.resourceId,
            transValue: item.japanese,
            transRemark: item.remark || '',
          })
        }
      })
    })

    if (saveRequests.length > 0) {
      const response = await saveCopywriting(saveRequests)
      if (response.success) {
        ElMessage.success(t('page.content.save_success'))
      }
      else {
        ElMessage.error(response.message || t('page.content.save_failed'))
      }
    }
  }
  catch (error) {
    console.error('保存文案信息失败:', error)
  }
}

// 获取文案信息
async function fetchCopywritingData() {
  const transId = getCurrentTransId()
  if (!transId) {
    ElMessage.warning(t('page.content.no_trans_id'))
    return
  }

  loading.value = true
  try {
    const response = await queryCopywriting(transId)
    if (response.success) {
      if (!response.data)
        return
      processCopywritingData(response.data)
    }
    else {
      ElMessage.error(response.message || t('page.content.fetch_failed'))
    }
  }
  catch (error) {
    console.error('获取文案信息失败:', error)
    ElMessage.error(t('page.content.fetch_failed'))
  }
  finally {
    loading.value = false
  }
}

// 处理文案数据
function processCopywritingData(data: CopywritingInfoVO) {
  // 处理商品名称
  productInfo.value = {
    chineseName: data.spuName?.sourceLanguage || '',
    japaneseName: data.spuName?.targetLanguage || '',
    link: data.spuLink || '',
    nameResourceId: data.nameResourceId || '',
  }

  // 处理SKU信息
  tableData.value = data.skuInfo?.map(sku => processSkuInfo(sku)) || []
}

// 处理SKU信息
function processSkuInfo(skuInfo: SkuCopywritingInfo) {
  const items = [] as any[]

  // 处理copywritingMap数组
  skuInfo.copywritingMap?.forEach((item: CopywritingVO) => {
    items.push({
      label: item.name || '',
      chinese: item.value?.sourceLanguage || '',
      japanese: item.value?.targetLanguage || '',
      resourceId: item.resourceId || '',
      remark: item.remark || '',
    })
  })

  return {
    id: skuInfo.sku || '',
    image: skuInfo.image || '',
    thumbnail: skuInfo.thumbnail || '',
    items,
  }
}

onMounted(() => {
  fetchCopywritingData()
})
</script>

<template>
  <div v-loading="loading" class="content-section">
    <div class="product-info-section">
      <div class="info-row three-columns">
        <div class="info-item">
          <div class="label">
            {{ $t('page.content.product_chinese_name') }}：
          </div>
          <div class="value">
            <div class="text-display">
              {{ productInfo.chineseName }}
            </div>
          </div>
        </div>
        <div class="info-item">
          <div class="label">
            {{ $t('page.content.product_japanese_name') }}：
          </div>
          <div class="value">
            <ElPopover
              :visible="showAIDialog"
              placement="bottom-start"
              hide-on-press-escape
              :trigger-keys="[]"
              :width="420"
            >
              <template #default>
                <AITranslateDialog
                  :model-value="showAIDialog"
                  :initial-text="productInfo.chineseName"
                  @translate-result="handleTranslateResult"
                  @close="showAIDialog = false"
                />
              </template>
              <template #reference>
                <el-input
                  v-if="isTranslatePage"
                  v-model="productInfo.japaneseName"
                  :placeholder="$t('page.content.enter_japanese_name')"
                />
                <div v-else class="text-display" :class="{ 'text-placeholder': !productInfo.japaneseName }">
                  {{ productInfo.japaneseName || $t('page.content.no_japanese_name') }}
                </div>
              </template>
            </ElPopover>
            <el-button v-if="isTranslatePage" type="primary" class="ai-button" @click="aiTranslate">
              AI
            </el-button>
            <el-button v-if="isTranslatePage" type="default" class="clear-button" @click="clearContent">
              {{ $t('page.content.clear') }}
            </el-button>
          </div>
        </div>
        <div class="info-item">
          <div class="label">
            {{ $t('page.content.remark') }}：
          </div>
          <div class="value">
            <el-input v-if="isTranslatePage" v-model="remark" :placeholder="$t('page.content.enter_remark')" />
            <div v-else class="text-display" :class="{ 'text-placeholder': !remark }">
              {{ remark || $t('page.content.no_remark') }}
            </div>
          </div>
        </div>
      </div>
      <!-- 商品链接 - 仅在设计和设计校验页面显示 -->
      <div v-if="isDesignPage || isDesignCheckPage" class="info-row">
        <div class="info-item full-width">
          <div class="label">
            {{ $t('page.content.product_link') }}：
          </div>
          <div class="value">
            <div class="text-display" :class="{ 'text-placeholder': !productInfo.link }">
              {{ productInfo.link || $t('page.content.no_link') }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="content-table-section">
      <div v-for="item in tableData" :key="item.id" class="sku-table-wrapper">
        <div class="sku-header">
          <span>SKU</span>
          <span>图片</span>
          <span>翻译</span>
        </div>
        <div class="sku-content">
          <div class="sku-id">
            {{ item.id }}
          </div>
          <div class="sku-image">
            <div v-if="item.thumbnail" class="sku-thumbnail">
              <img :src="item.thumbnail" alt="SKU缩略图">
            </div>
            <div v-else class="image-placeholder">
              <el-icon>
                <Picture />
              </el-icon>
            </div>
          </div>
          <div class="sku-translation">
            <div class="translation-table">
              <div class="table-header">
                <div class="header-cell">
                  字段名称
                </div>
                <div class="header-cell">
                  中文
                </div>
                <div class="header-cell">
                  日文
                </div>
              </div>
              <div v-for="(row, index) in item.items" :key="index" class="table-row">
                <div class="table-cell">
                  {{ row.label }}
                </div>
                <div class="table-cell">
                  {{ row.chinese }}
                </div>
                <div class="table-cell">
                  <el-input
                    v-if="isTranslatePage" v-model="row.japanese"
                    :placeholder="$t('page.content.enter_translation')"
                  />
                  <div v-else class="text-display" :class="{ 'text-placeholder': !row.japanese }">
                    {{ row.japanese || $t('page.content.no_translation') }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 保存按钮 - 仅在翻译页面显示 -->
    <div v-if="isTranslatePage" class="action-buttons">
      <el-button type="primary" @click="saveCopywritingData">
        {{ $t('page.content.save') }}
      </el-button>
      <el-button type="default" @click="fetchCopywritingData">
        {{ $t('page.content.refresh') }}
      </el-button>
    </div>
  </div>
</template>

<style scoped lang="scss">
.content-section {
  flex: 1;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 20px;
  overflow-y: auto;
  background-color: #f5f5f5;
}

.product-info-section {
  background-color: #fff;
  border-radius: 4px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);

  .info-row {
    display: flex;
    margin-bottom: 16px;
    gap: 20px;

    &:last-child {
      margin-bottom: 0;
    }

    &.three-columns {
      display: grid;
      grid-template-columns: 1fr 1fr 1fr;
    }
  }

  .info-item {
    flex: 1;
    display: flex;

    &.full-width {
      width: 100%;
    }

    .label {
      min-width: 80px;
      flex-shrink: 0;
      line-height: 32px;
      font-weight: 500;
    }

    .value {
      flex: 1;
      display: flex;
      gap: 10px;

      .el-input {
        flex: 1;
      }
    }
  }

  .ai-button {
    background-color: #399e96;
    color: white;
  }

  .text-display {
    min-height: 32px;
    line-height: 32px;
    padding: 0 12px;
    background-color: #f5f7fa;
    border-radius: 4px;
    color: #606266;

    &.text-placeholder {
      color: #909399;
      font-style: italic;
    }
  }

  .source-lang-select {
    width: 100%;
  }
}

.content-table-section {
  margin-bottom: 20px;
}

.sku-table-wrapper {
  background-color: #fff;
  border-radius: 4px;
  margin-bottom: 20px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  overflow: hidden;

  &:last-child {
    margin-bottom: 0;
  }
}

.sku-header {
  display: grid;
  grid-template-columns: 120px 200px 1fr;
  background-color: #f8f8f8;
  border-bottom: 1px solid #e0e0e0;
  padding: 10px 0;
  font-weight: 500;
  text-align: center;
}

.sku-content {
  display: grid;
  grid-template-columns: 120px 200px 1fr;
  min-height: 200px;
}

.sku-id {
  display: flex;
  justify-content: center;
  align-items: center;
  border-right: 1px solid #e0e0e0;
  font-weight: 500;
}

.sku-image {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 10px;
  border-right: 1px solid #e0e0e0;

  .image-placeholder {
    width: 150px;
    height: 150px;
    background-color: #f0f0f0;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 4px;

    .el-icon {
      font-size: 40px;
      color: #ccc;
    }
  }

  .sku-thumbnail {
    width: 150px;
    height: 150px;
    border-radius: 4px;
    overflow: hidden;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
}

.sku-translation {
  padding: 10px;
}

.translation-table {
  width: 100%;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
}

.table-header {
  display: grid;
  grid-template-columns: 150px 1fr 1fr;
  background-color: #f8f8f8;
  font-weight: 500;
}

.table-row {
  display: grid;
  grid-template-columns: 150px 1fr 1fr;
  border-top: 1px solid #e0e0e0;
}

.header-cell,
.table-cell {
  padding: 10px;
  border-right: 1px solid #e0e0e0;
  display: flex;
  align-items: center;

  &:last-child {
    border-right: none;
  }

  .el-input {
    width: 100%;
  }
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 20px;
}
</style>

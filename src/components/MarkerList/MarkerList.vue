<script setup lang="ts">
import type { PropType } from 'vue'
import { Delete, Document } from '@element-plus/icons-vue'
import { useI18n } from 'vue-i18n'

const props = defineProps({
  // 标记数组
  markers: {
    type: Array as PropType<Array<{
      id: string | number
      content: string
      rect?: any
    }>>,
    required: true,
  },
  // 是否可编辑（设计校验页面为true，设计页面为false）
  editable: {
    type: Boolean,
    default: false,
  },
  // 当前高亮的标记ID
  highlightedMarkerId: {
    type: [Number, String, null],
    default: null,
  },
})

const emit = defineEmits([
  'highlightMarker',
  'clearMarker',
  'update:markers',
])

const { t } = useI18n()

// 更新标记内容（仅在可编辑模式下）
function updateMarkerContent(marker: any, content: string) {
  if (!props.editable)
    return

  const updatedMarkers = [...props.markers]
  const index = updatedMarkers.findIndex(m => m.id === marker.id)
  if (index !== -1) {
    updatedMarkers[index] = { ...updatedMarkers[index], content }
    emit('update:markers', updatedMarkers)
  }
}

// 处理标记高亮
function handleHighlight(markerId: string | number | null) {
  emit('highlightMarker', markerId)
}

// 清除标记（仅在可编辑模式下）
function handleClearMarker(markerId: string | number) {
  if (!props.editable)
    return
  emit('clearMarker', markerId)
}

// 从标记ID字符串中获取数字ID（用于设计校验格式）
function getNumericId(id: string | number): number | string {
  if (typeof id === 'number')
    return id
  if (typeof id === 'string' && id.includes('-')) {
    return Number(id.split('-')[1])
  }
  return id
}
</script>

<template>
  <div class="design-notes">
    <div class="notes-header">
      <span>{{ t('page.design_check.markers') || '标记列表' }}</span>
    </div>

    <!-- 空状态 -->
    <div v-if="markers.length === 0" class="empty-markers">
      <el-icon><Document /></el-icon>
      <span>{{ t('page.design_check.no_markers') || '暂无标记' }}</span>
    </div>

    <!-- 标记列表 -->
    <div v-else class="markers-list">
      <div
        v-for="marker in markers"
        :key="marker.id"
        class="marker-item"
        :class="{
          'marker-highlighted': highlightedMarkerId === (typeof marker.id === 'string' ? getNumericId(marker.id) : marker.id),
          'marker-readonly': !editable,
        }"
        @mouseenter="handleHighlight(typeof marker.id === 'string' ? getNumericId(marker.id) : marker.id)"
        @mouseleave="handleHighlight(null)"
      >
        <!-- 可编辑模式（设计校验页面） -->
        <template v-if="editable">
          <el-input
            v-model="marker.content"
            type="textarea"
            :rows="3"
            :placeholder="t('page.design_check.marker_placeholder') || '请输入标记内容'"
            @input="updateMarkerContent(marker, $event)"
            @focus="handleHighlight(typeof marker.id === 'string' ? getNumericId(marker.id) : marker.id)"
            @blur="handleHighlight(null)"
          />
          <el-button class="clear-btn" link @click="handleClearMarker(marker.id)">
            <el-icon><Delete /></el-icon>
          </el-button>
        </template>

        <!-- 只读模式（设计页面） -->
        <template v-else>
          <div class="marker-content">
            {{ marker.content }}
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.design-notes {
  width: 300px;
  border-left: 1px solid var(--el-border-color-lighter);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: var(--el-bg-color);
  height: 100%;
}

.notes-header {
  padding: 12px 16px;
  font-weight: 500;
  font-size: 15px;
  color: var(--el-text-color-primary);
  border-bottom: 1px solid var(--el-border-color-lighter);
  background-color: var(--el-bg-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.empty-markers {
  padding: 30px 20px;
  text-align: center;
  color: var(--el-text-color-secondary);
  font-size: 14px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  .el-icon {
    font-size: 24px;
    margin-bottom: 8px;
    color: var(--el-text-color-placeholder);
  }
}

.markers-list {
  flex: 1;
  overflow-y: auto;
  padding: 12px 16px;
  scrollbar-width: thin;

  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: var(--el-border-color-light);
    border-radius: 2px;
  }
}

.marker-item {
  margin-bottom: 14px;
  position: relative;
  padding: 0;
  transition: all 0.25s ease;

  &.marker-readonly {
    padding: 10px 12px;
    border: 1px solid var(--el-border-color-lighter);
    border-radius: 4px;
    cursor: pointer;
  }
}

.marker-highlighted {
  &.marker-readonly {
    background-color: var(--el-color-primary-light-9);
    border: 1px solid var(--el-color-primary-light-5);
  }

  :deep(.el-textarea__inner) {
    border-color: var(--el-color-primary-light-5);
    background-color: var(--el-color-primary-light-9);
  }
}

.marker-content {
  white-space: pre-wrap;
  word-break: break-all;
  color: var(--el-text-color-regular);
  line-height: 1.5;
  font-size: 14px;
  padding: 2px 0;
}

.clear-btn {
  position: absolute;
  right: 4px;
  top: 4px;
  opacity: 0;
  transition: opacity 0.2s ease;
  color: var(--el-text-color-secondary);
  padding: 2px;
  font-size: 12px;

  &:hover {
    color: var(--el-color-danger);
  }
}

.marker-item:hover .clear-btn {
  opacity: 1;
}

:deep(.el-textarea__inner) {
  border-radius: 4px;
  transition: all 0.25s ease;
  resize: none;
  font-size: 14px;
  line-height: 1.5;
  padding: 8px 10px;

  &:focus {
    border-color: var(--el-color-primary);
  }
}
</style>

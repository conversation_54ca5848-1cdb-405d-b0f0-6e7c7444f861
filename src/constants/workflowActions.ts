export type WorkflowAction =
  | 'get_images'
  | 'submit_verification' // 翻译校验-提交校验
  | 'verification_approve' // 翻译校验-审核通过
  | 'verification_reject' // 翻译校验-审核不通过
  | 'corrected' // 已修正
  | 'submit_confirm' // 设计-提交确认
  | 'submit_text_check_correct' // 设计校验-提交文案检查
  | 'submit_text_check_reject' // 设计校验-提交文案检查
  | 'confirmed' // 已确认
  | 'return_for_correction' // 打回修正

interface NodeActionMap {
  [nodeId: string]: WorkflowAction[]
}

interface PageActionMap {
  translate: NodeActionMap
  design: NodeActionMap
  designCheck: NodeActionMap
}

export const pageActionMap: PageActionMap = {
  translate: {
    'sid_assign_interpreter': ['get_images'], // 待分配翻译：只有"获取图片"
    'sid_interpret': ['get_images', 'submit_verification'],
    'sid-interpretation-review': [
      'get_images',
      'verification_approve',
      'verification_reject',
    ],
    'sid-interpretation-correct': ['get_images', 'corrected'],
  },

  design: {
    'sid-design': ['submit_confirm'], // 设计对应中 → 提交确认
    'sid-design-correct': ['corrected'], // 设计返工 → 已修正
  },

  designCheck: {
    'sid-design-review': ['submit_text_check_correct', 'submit_text_check_reject'], // 设计确认 → 提交文案检查
    'sid-text-review': [
      'confirmed',
      'return_for_correction',
    ],
    'sid-correction-review': [
      'confirmed',
      'return_for_correction',
    ],
  },
}

import type {
  AddPhraseReq,
  CommonResult,
  CopywritingInfoVO,
  DesignInfoVO,
  ImageType,
  IPage,
  LanguageInfo,
  Layer,
  Phrase,
  SaveCopywritingReq,
  SaveDesignRemarkReq,
  SaveImageLayersReq,
  TranslateReq,
  TranslationResult,
  TranslationVO,
  UploadResult,
} from './types'
import axiosInstance from './axiosInstance'

// 翻译状态
export enum TranslateStatus {
  FAILED = -1,
  PROCESSING = 0,
  SUCCESS = 1,
  /** 图片尺寸不符合标准 15px to 8192px */
  INVALID_SIZE = -2,
}

/**
 * 图片信息
 */
export interface ImageInfo {
  /** 图片ID */
  id: string
  /** 主图/大图 URL */
  image: string
  /** 图层列表 */
  layers?: Layer[]
  /** 设计备注 */
  remark: string
  /**
   * 翻译状态
   * @remarks 可选值：FAILED_NO_RETRY, FAILED, PENDING, FINISHED
   */
  status?: TranslateStatus
  /** 缩略图 URL */
  thumbnail: string
  /** 背景图 */
  backgroundImg?: string
  /** 最终图片 */
  finalImage?: string
  /** 高度 */
  height?: number
  /** 宽度 */
  width?: number
  /** 翻译错误信息 */
  error?: string
}

/**
 * 添加常用语
 * @param req 添加常用语请求参数
 * @returns 通用返回结果
 */
export async function addPhrase(req: AddPhraseReq): Promise<CommonResult<void>> {
  const response = await axiosInstance.post('/api/image/translation/add-phrase', req)
  return response.data
}

/**
 * 获取图片列表
 * @param transId 翻译ID
 * @param type 图片类型（0,1,2）'0' 主图 '1' 详情 '2' sku
 * @returns 通用返回结果，包含图片信息列表
 */
export async function listImages(transId: string, type: ImageType): Promise<CommonResult<ImageInfo[]>> {
  const response = await axiosInstance.get('/api/image/translation/list-images', {
    params: {
      transId,
      type,
    },
  })
  return response.data
}

/**
 * 常用语列表
 * @returns 通用返回结果，包含常用语列表
 */
export async function listPhrases(): Promise<CommonResult<Phrase[]>> {
  const response = await axiosInstance.get('/api/image/translation/list-phrases')
  return response.data
}

/**
 * 保存设计备注
 * @param req 保存设计备注请求参数
 * @returns 通用返回结果
 */
export async function saveDesignRemark(req: SaveDesignRemarkReq): Promise<CommonResult<void>> {
  const response = await axiosInstance.post('/api/image/translation/save-design-remark', req)
  return response.data
}

/**
 * 保存翻译或设计
 * @param req 保存翻译或设计请求参数
 * @returns 通用返回结果
 */
export async function saveImageLayers(req: SaveImageLayersReq): Promise<CommonResult<void>> {
  const response = await axiosInstance.post('/api/image/translation/save-image-layers', req)
  return response.data
}

/**
 * GPT翻译
 * @param req GPT翻译请求参数
 * @returns 通用返回结果，包含翻译后的字符串
 */
export async function translate(req: TranslateReq): Promise<CommonResult<string>> {
  const response = await axiosInstance.post('/api/image/translation/translate', req)
  return response.data
}

/**
 * 上传素材压缩包
 * @param file 上传的文件
 * @param spu spu参数
 * @param sourceLang 源语言代码
 * @param targetLang 目标语言代码
 * @returns 通用返回结果，包含上传结果
 */
export async function uploadMaterial(file: File, spu: string, sourceLang: string, targetLang: string): Promise<CommonResult<UploadResult>> {
  const formData = new FormData()
  formData.append('file', file)

  const response = await axiosInstance.post('/api/image/translation/upload', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    params: {
      spu,
      sourceLang,
      targetLang,
    },
  })
  return response.data
}

/**
 * 根据SPU查询翻译列表
 * @param spu SPU编号
 * @param page 页码
 * @param size 每页大小
 * @returns 通用返回结果，包含分页的翻译列表
 */
export async function listTranslations(spu: string, page: number = 1, size: number = 10): Promise<CommonResult<IPage<TranslationVO>>> {
  const response = await axiosInstance.get('/api/image/translation/list-translations', {
    params: { spu, page, size },
  })
  return response.data
}

/**
 * 开始翻译
 * @param transId 翻译ID
 * @returns 通用返回结果
 */
export async function startTranslation(transId: string): Promise<CommonResult<void>> {
  const response = await axiosInstance.post(`/api/image/translation/start-translating?transId=${transId}`, null, {
    params: {
      transId,
    },
  })
  return response.data
}

/**
 * 轮询翻译结果
 * @param transId 翻译ID
 * @param type 图片类型
 * @returns 通用返回结果，包含翻译结果
 */
export async function pollingTranslation(transId: string, type: ImageType): Promise<CommonResult<TranslationResult>> {
  const response = await axiosInstance.get('/api/image/translation/polling-translation', {
    params: {
      transId,
      type,
    },
  })
  return response.data
}

/**
 * 上传设计或者翻译的图片
 * @param file 上传的文件
 * @returns 通用返回结果，包含图片key
 */
export async function uploadImage(file: File): Promise<CommonResult<string>> {
  const formData = new FormData()
  formData.append('file', file)
  const response = await axiosInstance.post('/api/image/translation/upload-image', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })
  return response.data
}

/**
 * 下载文件包
 * @param transId 翻译ID
 * @returns 包含二进制数据流和文件名的对象
 */
export async function downloadPackage(transId: string): Promise<{ blob: Blob, filename: string }> {
  const response = await axiosInstance.get(`/api/image/translation/download`, {
    params: { transId },
    responseType: 'blob',
  })
  const contentDisposition = response.headers['content-disposition']
  // 解析 filename，提供备用名
  let filename = 'download.zip' // Default filename
  if (contentDisposition) {
    const filenameMatch = contentDisposition.match(/filename\*?=(?:UTF-8'')?(.+)/i)
    if (filenameMatch && filenameMatch[1]) {
      filename = decodeURIComponent(filenameMatch[1].replace(/['"]/g, ''))
    }
    else {
      const fallbackMatch = contentDisposition.match(/filename="?([^"]+)"?/i)
      if (fallbackMatch && fallbackMatch[1]) {
        filename = fallbackMatch[1]
      }
    }
  }

  const blob = new Blob([response.data], { type: response.headers['content-type'] || 'application/zip' }) // 使用 headers 的 content-type 或默认 zip
  return { blob, filename }
}

/**
 * 重试翻译
 * @param imageId 图片ID
 * @returns 通用返回结果
 */
export async function retryTranslate(imageId: string): Promise<CommonResult<void>> {
  return axiosInstance.post('/api/image/translation/retry-translate', null, {
    params: { imageId },
  })
}

/**
 * 删除翻译任务
 * @param transId 翻译ID
 * @returns 通用返回结果
 */
export async function deleteTranslation(transId: string): Promise<CommonResult<void>> {
  const response = await axiosInstance.post('/api/image/translation/delete', null, {
    params: { transId },
  })
  return response.data
}

/**
 * 查询语言列表
 * @returns 通用返回结果，包含语言信息列表
 */
export async function listLanguages(): Promise<CommonResult<LanguageInfo[]>> {
  const response = await axiosInstance.get('/api/image/translation/language/list')
  return response.data
}

/**
 * 初始化产品ID
 * @param productId 产品ID
 * @returns 通用返回结果
 */
export async function initProduct(productId?: number): Promise<CommonResult<void>> {
  const response = await axiosInstance.post('/api/image/translation/init-product', null, {
    params: { productId },
  })
  return response.data
}

/**
 * 查询文案信息
 * @param transId 翻译ID
 * @returns 通用返回结果，包含文案信息
 */
export async function queryCopywriting(transId: string): Promise<CommonResult<CopywritingInfoVO>> {
  const response = await axiosInstance.get('/api/image/translation/query-copywriting', {
    params: { transId },
  })
  return response.data
}

/**
 * 查询设计稿信息
 * @param transId 翻译ID
 * @param type 图片类型（0,1,2,3,4） 0 主图 1 详情 2 sku 3 安装说明
 * @returns 通用返回结果，包含设计稿信息列表
 */
export async function queryDesign(transId: string, type: ImageType): Promise<CommonResult<DesignInfoVO[]>> {
  const response = await axiosInstance.get('/api/image/translation/query-design', {
    params: { transId, type },
  })
  return response.data
}

/**
 * 保存文案信息
 * @param saveCopywritingReq 保存文案请求参数列表
 * @returns 通用返回结果
 */
export async function saveCopywriting(saveCopywritingReq: SaveCopywritingReq[]): Promise<CommonResult<void>> {
  const response = await axiosInstance.post('/api/image/translation/save-copywriting', saveCopywritingReq)
  return response.data
}

/**
 * 保存设计稿信息
 * @param designInfoVO 设计稿信息
 * @returns 通用返回结果
 */
export async function saveDesign(designInfoVO: DesignInfoVO): Promise<CommonResult<void>> {
  const response = await axiosInstance.post('/api/image/translation/save-design', designInfoVO)
  return response.data
}

/**
 * 上传设计稿
 * @param transId 翻译ID
 * @param imageType 图片类型（0,1,2,3,4） 0 主图 1 详情 2 sku 3 安装说明
 * @param designFiles 设计稿文件列表
 * @returns 通用返回结果
 */
export async function uploadDesign(transId: string, imageType: ImageType, designFiles: File[]): Promise<CommonResult<void>> {
  const formData = new FormData()

  // 添加多个文件
  designFiles.forEach((file) => {
    formData.append('designFiles', file)
  })

  const response = await axiosInstance.post('/api/image/translation/upload-design', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    params: {
      transId,
      imageType,
    },
  })
  return response.data
}

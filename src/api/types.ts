import type { ImageInfo } from './imageTranslationApi'

export interface IPage<T> {
  records: T[]
  total: number
  size: number
  current: number
  pages?: number // 可选，根据后端实际返回添加
}
// 通用返回结果
export interface CommonResult<T = any> {
  code: string
  data: T
  message: string
  success: boolean
}

// 添加常用语请求
export interface AddPhraseReq {
  content: string // 常用语内容
  title: string // 常用语标题
}

// 翻译状态
export enum TranslateStatus {
  FAILED = -1,
  PROCESSING = 0,
  SUCCESS = 1,
}

// 图片类型（0,1,2,3）'0' 主图 '1' 详情 '2' sku '3' 安装说明
export enum ImageType {
  MAIN = '0',
  DETAIL = '1',
  SKU = '2',
  INSTALLATION = '3',
}

// 图片信息
export interface ImageInfoExtend extends ImageInfo {
  type: ImageType
  done?: boolean
  backgroundImgBlob?: Blob
  originalImageBlob?: Blob
  backgroundImgFilename?: string
  istranslateChanged?: boolean
  isCanvasChanged?: boolean
  originalLayers?: Layer[]
}

// 常用语
export interface Phrase {
  content: string
  id: number
  title: string
}

// 背景图片接口
export interface BackgroundImage {
  label: string
  backgroundImg: string
  width?: number
  height?: number
  x?: number
  y?: number
}

// 基础层属性
interface BaseLayer {
  id: number
  top: number
  left: number
  width: number
  height: number
  label: string
  remark: string | null
  zIndex: number
  type: LayerType
}

// Image 类型的层接口
export interface ImageLayer extends BaseLayer {
  type: LayerType.IMAGE
  backgroundImg: string
  src?: string
}

// Text 类型的层接口
export interface TextLayer extends BaseLayer {
  type: LayerType.TEXT
  backgroundColor: string | null
  color: string
  textAlign: string
  letterSpacing: number
  content: string
  fontFamily: string
  fontSize: number
  lineHeight: number
  direction: number | null
  ocrContent: string
  numlines?: number
  strokeWidth?: number
  // align?: string // Swagger TextLayerVO 有 align
  fontWeight?: string // Swagger TextLayerVO 有 fontWeight
  stroke?: string
  borderRadius?: number
  // numlines?: number
  fill?: string
  visible?: boolean
  isForeignLayer?: boolean
}

// 联合类型，表示不同类型的层
export type Layer = ImageLayer | TextLayer

// JSON数据接口
export interface JsonData {
  backgroundImg: string
  layers: Layer[]
}

// 顶层接口，表示整个数据结构
export interface ImageData {
  imageId: string
  status: number
  width: number
  height: number
  image: string
  inPaintingImage: string
  finalImage: string
  layers?: Layer[]
  error?: string
}

// 翻译结果
export interface TranslationResult {
  done: boolean
  imageTranslations: ImageData[]
  transId: string
}

// 上传结果
export interface UploadResult {
  transId: string
  spu: string
}

// 语言信息视图
export interface LanguageInfo {
  code: string
  name: string
}

// 图层类型
export enum LayerType {
  IMAGE = 'image',
  TEXT = 'text',
}

// 保存设计备注请求
export interface SaveDesignRemarkReq {
  imageId: string
  remark: string
}

// 保存翻译或设计请求
export interface SaveImageLayersReq {
  imageId?: string
  layerContent?: string
  file?: string // 上传的图片key
}

// GPT翻译请求
export interface TranslateReq {
  phraseId?: number
  prompt?: string
  source?: string
}

// 上传素材压缩包响应
export interface UploadResult {
  transId: string
}

// 翻译列表项
export interface TranslationVO {
  spu: string
  transId: string
  creator: string
  createTime: string
  updateTime: string
}

// 设计信息视图
export interface DesignInfoVO {
  designId?: string
  designLink?: string
  remark?: string
}

// SKU文案信息
export interface SkuCopywritingInfo {
  sku: string
  image: string
  thumbnail: string
  copywritingMap: Record<string, any>
}

// 文案信息视图
export interface CopywritingInfoVO {
  transId: string
  spuName: Record<string, string>
  spuLink: string
  skuInfo: SkuCopywritingInfo[]
  nameResourceId?: string
}

// 保存文案请求
export interface SaveCopywritingReq {
  resourceId: string
  transValue: string
  transRemark: string
}

// 工单按状态统计请求
export interface TicketInstStatusCountReqVO {
  userIds?: number[] // 工单相关的用户ID
}

// 工单按状态统计响应
export interface TicketInstStatusCountResultVO {
  userId?: number // 工单相关的用户ID
  count?: { [key: string]: number } //  用户视角的工单状态分组统计结果，key为状态（TO_BE_PROCESSED-工单等待当前用户处理，PROCESSED_BEFORE-工单曾被当前用户处理过，COMPLETED-工单已关闭），value为计数值
}

// 用户简单信息
export interface UserSimpleInfoVO {
  userId: number
  userName: string
  avatar?: string
}

// 工单详情状态枚举
export enum TicketInstDetailVOTicketStatusEnum {
  PROCESSING = 0, // 进行中
  COMPLETED = 1, // 已完成
}

// 当前节点类型枚举
export enum TicketInstDetailVOCurrentNodeTypeEnum {
  SUBMIT = 0, // 提交
  APPROVE = 1, // 审核
}

// 当前节点状态枚举
export enum TicketInstDetailVOCurrentNodeStatusEnum {
  TO_BE_ACCEPTED = 0, // 待处理
  ACCEPTED = 1, // 已接受
  PROCESSED = 2, // 已处理
  TO_BE_APPROVED = 3, // 待审核
  APPROVED = 4, // 已审核
  REJECTED = 5, // 已拒绝
}

// 工单详情
export interface TicketInstDetailVO {
  ticketInstId?: number // 工单ID
  wfInstId?: number // 工单关联的工作流实例ID
  formInstId?: number // 工单关联的表单实例ID
  priority?: number // 优先级，值越大优先级越高
  creator?: UserSimpleInfoVO // 创建人信息
  createTime?: string // 创建时间
  updateTime?: string // 更新时间
  ticketStatus?: TicketInstDetailVOTicketStatusEnum // 工单状态
  currentNodeId?: string // 当前节点ID
  currentNodeType?: TicketInstDetailVOCurrentNodeTypeEnum // 当前节点类型
  currentNodeName?: string // 当前节点名称
  currentNodeStatus?: TicketInstDetailVOCurrentNodeStatusEnum // 当前节点处理状态
  currentNodeProcessor?: UserSimpleInfoVO[] // 当前节点处理人
  formData?: string // 填写的表单数据
}

// 工单处理操作类型枚举
export enum TicketInstProcessReqVOOpTypeEnum {
  ACCEPT = 1, // 接受工单
  SUBMIT = 2, // 提交工单
  APPROVE = 3, // 审批工单
  TRANSFER = 4, // 转移
}

// 工单处理操作结果枚举
export enum TicketInstProcessReqVOOpResultEnum {
  APPROVED = 0, // 审核通过
  REJECTED = 1, // 审核拒绝
}

// 工单处理请求
export interface TicketInstProcessReqVO {
  opType: TicketInstProcessReqVOOpTypeEnum // 操作类型
  opResult?: TicketInstProcessReqVOOpResultEnum // 操作结果
  opDesc?: string // 操作说明
  formData?: string // 提交的表单数据
}

// 工单列表查询请求
export interface TicketInstQueryReqVO {
  pageNum?: number // 当前页码
  pageSize?: number // 分页大小
  orderBy?: string // 排序规则
  ticketDefId?: number // 工单类型ID
  userId?: number // 工单相关的用户ID
  relevantObjIds?: string[] // 工单关联的对象ID
  ticketInstId?: number // 工单ID
  priority?: number // 优先级
  includedTicketStatusList?: TicketInstQueryReqVOIncludedTicketStatusListEnum[] // 待过滤的工单状态列表
  includedTicketUserStatusList?: TicketInstQueryReqVOIncludedTicketUserStatusListEnum[] // 待过滤的用户视角工单处理状态
  keyword?: string // 搜索关键词
}

// 工单状态枚举
export enum TicketInstQueryReqVOIncludedTicketStatusListEnum {
  PROCESSING = 0, // 进行中
  COMPLETED = 1, // 已完成
}

// 用户视角工单处理状态枚举
export enum TicketInstQueryReqVOIncludedTicketUserStatusListEnum {
  TO_BE_PROCESSED = 0, // 工单等待当前用户处理
  PROCESSED_BEFORE = 1, // 工单曾被当前用户处理过
  COMPLETED = 2, // 已完成
}

// 工单查询响应
export interface TicketInstQueryRespVO {
  ticketInstId?: number
  ticketDefId?: number
  ticketDefName?: string
  priority?: number
  creator?: UserSimpleInfoVO
  createTime?: string
  updateTime?: string
  ticketStatus?: number
  currentNodeId?: string
  currentNodeName?: string
  currentNodeStatus?: number
  currentNodeProcessor?: UserSimpleInfoVO[]
  relevantObjId?: string
}

// 工单操作日志
export interface TicketInstLogVO {
  id?: number // 日志ID
  nodeId?: string // 操作关联的工作流节点ID
  nodeName?: string // 操作关联的工作流节点名称
  opType?: string // 操作类型
  opResult?: string // 操作类型
  opDesc?: string // 操作说明
  operator?: UserSimpleInfoVO // 操作人信息
  createTime?: string // 创建时间
}

// 创建工作流实例请求
export interface TicketInstStartReqVO {
  ticketDefCode: string // 工单编码
  instName?: string // 工作流实例名称
  variables?: { [key: string]: object } // 工作流实例的上下文变量
  formData: string // 和工单一起提交的表单数据
  relevantObjId?: string // 工单关联的对象ID
  priority?: number // 优先级，数值越大优先级越高
}

// 工单转移请求
export interface TicketInstTransferReqVO {
  assignee?: number // 接受人ID
  opDesc?: string // 操作说明
}

// 表单定义
export interface FormDefVO {
  id?: number
  name?: string
  code?: string
  desc?: string
  formSchema?: string
}

// 表单字段权限
export interface FormFieldAuthVO {
  id?: number
  nodeId?: string
  nodeName?: string
  fieldPath?: string
  fieldName?: string
  authType?: number
}

// 工作流定义
export interface WorkflowDefinitionVO {
  id?: number
  name?: string
  code?: string
  desc?: string
  wfSchema?: string
}

// 工作流人工节点定义
export interface WorkflowManualNodeDefinitionVO {
  id?: number
  nodeId?: string
  nodeName?: string
  nodeType?: number
  assigneeType?: number
  assigneeList?: UserSimpleInfoVO[]
}

// 工单配置
export interface TicketDefVO {
  id?: number
  name?: string
  code?: string
  desc?: string
  ticketRelatedFormDef?: FormDefVO
  formFieldAuthList?: FormFieldAuthVO[]
  wfDef?: WorkflowDefinitionVO
  wfManualNodeDefList?: WorkflowManualNodeDefinitionVO[]
}

// 表单定义更新请求
export interface FormDefUpdateReqVO {
  id?: number
  name?: string
  desc?: string
  formSchema?: string
}

// 表单字段权限更新请求
export interface FormFieldAuthUpdateReqVO {
  id?: number
  nodeId?: string
  fieldPath?: string
  authType?: number
}

// 工单配置更新请求
export interface TicketDefUpdateReqVO {
  id: number
  name?: string
  desc?: string
  ticketRelatedFormDef?: FormDefUpdateReqVO
  formFieldAuthList?: FormFieldAuthUpdateReqVO[]
}

// 工单类型
export interface TicketTypeVO {
  id?: number // 工单配置ID
  name?: string // 工单名称
  code?: string // 工单编码
  desc?: string // 工单描述
  manualCreationAllowed?: boolean // 是否允许手工创建工单
}

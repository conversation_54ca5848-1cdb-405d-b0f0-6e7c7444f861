/**
 * 用户简单信息
 * @export
 * @interface UserSimpleInfoVO
 */
export interface UserSimpleInfoVO {
  /**
   * 用户ID
   * @type {number}
   * @memberof UserSimpleInfoVO
   */
  userId?: number
  /**
   * 用户名
   * @type {string}
   * @memberof UserSimpleInfoVO
   */
  userName?: string
  /**
   * 头像
   * @type {string}
   * @memberof UserSimpleInfoVO
   */
  avatar?: string
}

/**
 * 工单查询结果
 * @export
 * @interface TicketInstListItemVO
 */
export interface TicketInstListItemVO {
  /**
   * 工单ID
   * @type {number}
   * @memberof TicketInstListItemVO
   */
  id?: number
  /**
   * 工单关联的对象ID
   * @type {string}
   * @memberof TicketInstListItemVO
   */
  relevantObjId?: string
  /**
   * 工单类型ID
   * @type {number}
   * @memberof TicketInstListItemVO
   */
  ticketDefId?: number
  /**
   * 工单类型code
   * @type {string}
   * @memberof TicketInstListItemVO
   */
  ticketDefCode?: string
  /**
   * 工单类型名称
   * @type {string}
   * @memberof TicketInstListItemVO
   */
  ticketDefName?: string
  /**
   * 工单关联的工作流实例ID
   * @type {number}
   * @memberof TicketInstListItemVO
   */
  wfInstId?: number
  /**
   * 工单关联的表单实例ID
   * @type {number}
   * @memberof TicketInstListItemVO
   */
  formInstId?: number
  /**
   * 优先级，值越大优先级越高
   * @type {number}
   * @memberof TicketInstListItemVO
   */
  priority?: number
  /**
   *
   * @type {UserSimpleInfoVO}
   * @memberof TicketInstListItemVO
   */
  creator?: UserSimpleInfoVO
  /**
   * 工单状态，0:PROCESSING-进行中，1:COMPLETED-已关闭
   * @type {number}
   * @memberof TicketInstListItemVO
   */
  status?: TicketInstListItemVOStatusEnum
  /**
   * 创建时间
   * @type {string}
   * @memberof TicketInstListItemVO
   */
  createTime?: string
  /**
   * 更新时间
   * @type {string}
   * @memberof TicketInstListItemVO
   */
  updateTime?: string
}

export const TicketInstListItemVOStatusEnum = {
  PROCESSING: 0,
  COMPLETED: 1,
} as const

// eslint-disable-next-line ts/no-redeclare
export type TicketInstListItemVOStatusEnum = typeof TicketInstListItemVOStatusEnum[keyof typeof TicketInstListItemVOStatusEnum]

/**
 * API分页结果
 * @export
 * @interface PageVOTicketInstListItemVO
 */
export interface PageVOTicketInstListItemVO {
  /**
   * 当前页码
   * @type {number}
   * @memberof PageVOTicketInstListItemVO
   */
  pageNum?: number
  /**
   * 每页大小
   * @type {number}
   * @memberof PageVOTicketInstListItemVO
   */
  pageSize?: number
  /**
   * 总记录数
   * @type {number}
   * @memberof PageVOTicketInstListItemVO
   */
  total?: number
  /**
   * 总页数
   * @type {number}
   * @memberof PageVOTicketInstListItemVO
   */
  pages?: number
  /**
   * 当前页结果集
   * @type {Array<TicketInstListItemVO>}
   * @memberof PageVOTicketInstListItemVO
   */
  records?: Array<TicketInstListItemVO>
}

/**
 * 返回结果
 * @export
 * @interface ResultVOPageVOTicketInstListItemVO
 */
export interface ResultVOPageVOTicketInstListItemVO {
  /**
   * 错误码
   * @type {number}
   * @memberof ResultVOPageVOTicketInstListItemVO
   */
  code?: number
  /**
   * 错误消息
   * @type {string}
   * @memberof ResultVOPageVOTicketInstListItemVO
   */
  message?: string
  /**
   *
   * @type {PageVOTicketInstListItemVO}
   * @memberof ResultVOPageVOTicketInstListItemVO
   */
  data?: PageVOTicketInstListItemVO
  /**
   * 是否成功
   * @type {boolean}
   * @memberof ResultVOPageVOTicketInstListItemVO
   */
  success?: boolean
}

import type { ResultVOPageVOTicketInstListItemVO } from './ticketTypes'
import type {
  CommonResult,
  IPage,
  TicketDefUpdateReqVO,
  TicketDefVO,
  TicketInstDetailVO,
  TicketInstLogVO,
  TicketInstProcessReqVO,
  TicketInstQueryReqVO,
  TicketInstQueryRespVO,
  TicketInstStartReqVO,
  TicketInstStatusCountReqVO,
  TicketInstStatusCountResultVO,
  TicketInstTransferReqVO,
  TicketTypeVO,
} from './types'
import axiosInstance from './gatewayAxios'

/**
 * 按状态分组统计工单
 * @param ticketInstStatusCountReqVO 状态统计请求参数
 * @returns 通用返回结果，包含工单状态统计信息
 */
export async function countByStatus(ticketInstStatusCountReqVO: TicketInstStatusCountReqVO): Promise<CommonResult<TicketInstStatusCountResultVO[]>> {
  const response = await axiosInstance.post('/gaia-workflow/api/ticket-instances/count-by-status', ticketInstStatusCountReqVO)
  return response.data
}

/*	*
 * 获取工单详情
 * @param ticketInstId 工单实例ID
 * @returns 通用返回结果，包含工单详情
 */
export async function getDetailById(ticketInstId: string): Promise<CommonResult<TicketInstDetailVO>> {
  const response = await axiosInstance.get(`/gaia-workflow/api/ticket-instances/${ticketInstId}`)
  return response.data
}

/**
 * 处理工单(接受/提交/审核)
 * @param ticketInstId 工单实例ID
 * @param ticketInstProcessReqVO 工单处理请求参数
 * @returns 通用返回结果
 */
export async function process(ticketInstId: string, ticketInstProcessReqVO: TicketInstProcessReqVO): Promise<CommonResult<void>> {
  const response = await axiosInstance.post(`/gaia-workflow/api/ticket-instances/${ticketInstId}/process`, ticketInstProcessReqVO)
  return response.data
}

/**
 * 查询工单列表
 * @param ticketInstQueryReqVO 工单查询请求参数
 * @returns 通用返回结果，包含分页的工单列表
 */
export async function query(ticketInstQueryReqVO: TicketInstQueryReqVO): Promise<ResultVOPageVOTicketInstListItemVO> {
  const response = await axiosInstance.post('/gaia-workflow/api/ticket-instances/query', ticketInstQueryReqVO)
  return response.data
}

/**
 * 查询工单的操作日志
 * @param ticketInstId 工单实例ID
 * @returns 通用返回结果，包含工单操作日志列表
 */
export async function queryLogs(ticketInstId: string): Promise<CommonResult<TicketInstLogVO[]>> {
  const response = await axiosInstance.get(`/gaia-workflow/api/ticket-instances/${ticketInstId}/logs`)
  return response.data
}

/**
 * 启动工作流实例
 * @param ticketInstStartReqVO 工单启动请求参数
 * @returns 通用返回结果
 */
export async function startWorkflowInstance(ticketInstStartReqVO: TicketInstStartReqVO): Promise<CommonResult<void>> {
  const response = await axiosInstance.post('/gaia-workflow/api/ticket-instances/start', ticketInstStartReqVO)
  return response.data
}

/**
 * 转移工单
 * @param ticketInstId 工单实例ID
 * @param ticketInstTransferReqVO 工单转移请求参数
 * @returns 通用返回结果
 */
export async function transfer(ticketInstId: string, ticketInstTransferReqVO: TicketInstTransferReqVO): Promise<CommonResult<void>> {
  const response = await axiosInstance.post(`/gaia-workflow/api/ticket-instances/${ticketInstId}/transfer`, ticketInstTransferReqVO)
  return response.data
}

/**
 * 获取工单配置
 * @param ticketDefId 工单配置ID
 * @returns 通用返回结果，包含工单配置详情
 */
export async function getTicketDefById(ticketDefId: string): Promise<CommonResult<TicketDefVO>> {
  const response = await axiosInstance.get(`/gaia-workflow/api/ticket-definitions/${ticketDefId}`)
  return response.data
}

/**
 * 获取工单类型
 * @param manualCreationAllowed 是否允许手动创建
 * @returns 通用返回结果，包含工单类型列表
 */
export async function queryTicketTypeList(manualCreationAllowed?: boolean): Promise<CommonResult<TicketTypeVO[]>> {
  const response = await axiosInstance.get('/gaia-workflow/api/ticket-definitions', {
    params: {
      manualCreationAllowed,
    },
  })
  return response.data
}

/**
 * 更新工单配置
 * @param ticketDefUpdateReqVO 工单配置更新请求参数
 * @returns 通用返回结果
 */
export async function updateTicketDef(ticketDefUpdateReqVO: TicketDefUpdateReqVO): Promise<CommonResult<void>> {
  const response = await axiosInstance.put('/gaia-workflow/api/ticket-definitions', ticketDefUpdateReqVO)
  return response.data
}

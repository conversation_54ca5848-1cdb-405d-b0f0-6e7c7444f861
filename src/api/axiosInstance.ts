import router from '@/router'
import { useUserStore } from '@/stores/user'
import axios from 'axios'
import { ElMessage } from 'element-plus'

const axiosInstance = axios.create({
  baseURL: '/',
  headers: { 'content-type': 'application/json' },
  withCredentials: false,
})

// 请求拦截器
axiosInstance.interceptors.request.use(
  (config) => {
    const userStore = useUserStore()
    const token = userStore.userInfo?.jwt

    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }

    return config
  },
  (error) => {
    return Promise.reject(error)
  },
)

// 响应拦截器
axiosInstance.interceptors.response.use(
  (response) => {
    const { data } = response
    if (typeof data === 'object' && 'success' in data && !data.success) {
      // 如果请求成功但业务失败，显示错误信息
      ElMessage.error({ message: data.message || '操作失败', grouping: true })

      if (data.code === '110107') {
        router.push('/login')
      }

      return Promise.reject(data)
    }
    return response
  },
  (error) => {
    if (error.response) {
      const { status } = error.response

      switch (status) {
        case 401:
          // 未授权，跳转到登录页
          ElMessage.error('请先登录')
          router.push('/login')
          break
        case 403:
          // 权限不足
          ElMessage.error('权限不足')
          break
        case 404:
          ElMessage.error('请求的资源不存在')
          break
        default:
          ElMessage.error({ message: '服务器错误', grouping: true })
      }
    }
    else {
      ElMessage.error({ message: '网络错误，请检查网络连接', grouping: true })
    }
    return Promise.reject(error)
  },
)

export default axiosInstance

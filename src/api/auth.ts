import gatewayAxios from './gatewayAxios'

export interface UserLoginReq {
  userName: string
  password: string
  captcha: string
  captchaKey: string
}

export interface UserInfo {
  userId: number
  userName: string
  name: string
  permissionCodes: PermissionsEnum[]
  jwt: string
}

export enum PermissionsEnum {
  TRANSLATE = 'TRANSLATE',
  TRANSLATE_REVIEW = 'TRANSLATE_REVIEW',
  DESIGN = 'DESIGN',
  DESIGN_REVIEW = 'DESIGN_REVIEW',
}

export interface CaptchaValue {
  key: string
  text: string
  textImage: string
}

export interface CommonResult<T> {
  code: string
  data: T
  message: string
  success: boolean
}

// 生成验证码
export async function generateCaptcha() {
  const response = await gatewayAxios.post<CommonResult<CaptchaValue>>(
    `/gaia-user/api/users/generate-captcha`,
  )
  return response.data
}

// 用户登录
export async function login(data: UserLoginReq) {
  const response = await gatewayAxios.post<CommonResult<UserInfo>>(
    `/gaia-user/api/users/login`,
    data,
  )
  return response.data
}

// 用户退出登录
export async function logout(jwt?: string) {
  const response = await gatewayAxios.post<CommonResult<null>>(
    `/gaia-user/api/users/logout`,
    { jwt },
  )
  return response.data
}

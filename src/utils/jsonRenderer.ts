import type { BackgroundImage, ImageLayer, JsonD<PERSON>, Layer, TextLayer } from '@/api/types'
import type { IEditor } from '@kuaitu/core'
import { useTextBox } from '@/composables/useTextBox'
import { fabric } from 'fabric'

export async function renderJsonData(editor: IEditor, canvas: fabric.Canvas, jsonData: JsonData, showChineseLayer = false, showForeignLayer = true) {
  // 选择性清除非workspace对象
  const objects = editor.fabricCanvas?.getObjects()
  objects?.forEach((obj: any) => {
    if (obj.id !== 'workspace') {
      editor.fabricCanvas && editor.fabricCanvas.remove(obj)
    }
  })

  const imgEl = await loadImage(jsonData.backgroundImg)

  await renderBackground(editor, canvas, imgEl, {
    backgroundImg: jsonData.backgroundImg,
    label: 'background',
  })

  await renderTextElements(editor, jsonData.layers || [], showChineseLayer, showForeignLayer)

  // 处理中文图层
  // const chineseLayers = await processChineseLayer(editor, jsonData.children)
  // if (chineseLayers) {
  //   chineseLayers.forEach((layer) => {
  //     // 直接添加到canvas，不使用editor.addBaseType
  //     canvas.add(layer)
  //     // 确保图层在正确的位置
  //     layer.setCoords()
  //   })
  //   // 将中文图层信息保存到editor实例中
  //   ;(editor as any).chineseLayers = chineseLayers
  // }

  canvas.renderAll()
}

async function loadImage(src: string): Promise<HTMLImageElement> {
  return new Promise((resolve, reject) => {
    const img = new Image()
    img.crossOrigin = 'anonymous' // 添加跨域支持
    img.onload = () => resolve(img)
    img.onerror = reject
    img.src = src
  })
}

async function renderBackground(
  editor: IEditor,
  canvas: fabric.Canvas,
  imgEl: HTMLImageElement,
  bgImage: BackgroundImage,
) {
  const naturalWidth = imgEl.naturalWidth
  const naturalHeight = imgEl.naturalHeight
  const scaleX = (bgImage.width ?? naturalWidth) / naturalWidth
  const scaleY = (bgImage.height ?? naturalHeight) / naturalHeight

  editor.setSize(naturalWidth, naturalHeight)

  const imgInstance = await editor.createImgByElement(imgEl) as fabric.Image
  imgInstance.set({
    left: bgImage.x ?? 0,
    top: bgImage.y ?? 0,
    scaleX,
    scaleY,
    originX: 'left',
    originY: 'top',
    hasControls: false,
    selectable: false,
    evented: false,
  })

  canvas.add(imgInstance)
  imgInstance.sendToBack()

  // 检查workspace是否存在
  const updatedWorkspace = editor.getWorkspase()
  if (updatedWorkspace) {
    updatedWorkspace.sendToBack()
  }

  imgEl.remove()
}

async function renderTextElements(editor: IEditor, elements: (TextLayer | ImageLayer)[], showChineseLayer = false, showForeignLayer = true) {
  const { createRectWithText } = useTextBox()
  const textElements = elements.filter((item): item is TextLayer =>
    (item as any).type === 'text' && ((item as any).content || (item as any).ocrContent),
  )

  const canvas = editor.fabricCanvas
  if (!canvas)
    return

  // 获取当前画布中的对象数量作为基准索引
  const baseIndex = canvas.getObjects().length

  // 先添加外文图层，设置较低的层级
  textElements.forEach((textData, index) => {
    const rectWithText = createRectWithText(editor, {
      text: textData.content || textData.ocrContent || '',
      rectOptions: {
        width: textData.width,
        height: textData.height,
        left: textData.left,
        top: textData.top,
        fill: textData.fill || 'transparent',
        stroke: textData.stroke || '#399E96',
        strokeWidth: textData.strokeWidth || 0,
        visible: showForeignLayer,
        id: textData.id,
        data: {
          borderRadius: textData.borderRadius || 0,
          isForeignLayer: true,
        },
      },
      textOptions: {
        fontSize: textData.fontSize,
        fill: textData.color,
        width: textData.width,
        visible: showForeignLayer,
        splitByGrapheme: true,
        textAlign: 'left',
        data: {
          isForeignLayer: true,
          numlines: textData.numlines,
        },
      },
    })

    // 使用 moveTo 来设置对象顺序
    canvas.moveTo(rectWithText as unknown as fabric.Object, baseIndex + index)
  })

  // 后添加中文图层，设置较高的层级
  const chineseBaseIndex = baseIndex + textElements.length // 确保中文图层在外语图层之上
  textElements.forEach((textData, index) => {
    const rectWithText = createRectWithText(editor, {
      text: textData.ocrContent || '',
      rectOptions: {
        width: textData.width,
        height: textData.height,
        left: textData.left,
        top: textData.top,
        fill: textData.fill || 'transparent',
        stroke: textData.stroke || '#399E96',
        strokeWidth: textData.strokeWidth || 0,
        visible: showChineseLayer,
        id: textData.id,
        data: {
          borderRadius: textData.borderRadius || 0,
          isForeignLayer: false,
        },
      },
      textOptions: {
        fontSize: textData.fontSize,
        fill: textData.color,
        width: textData.width,
        visible: showChineseLayer,
        splitByGrapheme: true,
        textAlign: 'left',
        data: {
          isForeignLayer: false,
          numlines: textData.numlines,
        },
      },
    })

    // 使用 moveTo 来设置对象顺序
    canvas.moveTo(rectWithText as unknown as fabric.Object, chineseBaseIndex + index)
  })

  canvas.renderAll()
  canvas.discardActiveObject()
}

// 加载并处理中文图层
// eslint-disable-next-line unused-imports/no-unused-vars
async function processChineseLayer(editor: IEditor, elements: (BackgroundImage | TextLayer | ImageLayer)[]) {
  const chineseImage = elements.find((item): item is ImageLayer => (item as any).type === 'image')

  if (!chineseImage?.src)
    return null

  try {
    const img = await loadImage(chineseImage.src)
    const textElements = elements.filter((item): item is TextLayer =>
      (item as any).type === 'text' && (item as any).ocrContent,
    )

    const chineseLayers = await Promise.all(
      textElements.map(async (textData) => {
        if (!textData.left || !textData.top || !textData.width || !textData.height)
          return null

        // 创建临时canvas进行裁剪
        const tempCanvas = document.createElement('canvas')
        tempCanvas.width = textData.width
        tempCanvas.height = textData.height
        const ctx = tempCanvas.getContext('2d')
        if (!ctx)
          return null

        // 在临时canvas上绘制裁剪后的图像
        ctx.drawImage(
          img,
          textData.left,
          textData.top,
          textData.width,
          textData.height,
          0,
          0,
          textData.width,
          textData.height,
        )

        // 将裁剪后的图像转换为fabric.Image对象
        return new Promise<fabric.Image>((resolve) => {
          fabric.Image.fromURL(tempCanvas.toDataURL(), (fabricImg) => {
            fabricImg.set({
              left: textData.left,
              top: textData.top,
              width: textData.width,
              height: textData.height,
              selectable: false,
              visible: false,
              crossOrigin: 'anonymous',
              ignoreGuideline: true,
            } as any)
            resolve(fabricImg)
          }, { crossOrigin: 'anonymous' })
        })
      }),
    )

    return chineseLayers.filter((layer): layer is fabric.Image => layer !== null)
  }
  catch (error) {
    console.error('处理中文图层失败:', error)
    return null
  }
}

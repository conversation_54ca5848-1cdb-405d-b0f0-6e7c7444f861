import axiosInstance from '@/api/axiosInstance'
import { ImageType } from '@/api/types'

/**
 * 从OSS URL中提取指定格式的文件名
 * @param url 完整的OSS URL
 * @returns 提取的文件名（格式如：xejzl0043_main_01）
 */
export function extractOssFilename(url: string): string {
  try {
    const urlWithoutQuery = url.split('?')[0]
    const filenameWithExt = urlWithoutQuery.split('/').pop() || ''
    let filename = filenameWithExt.split('.')[0]
    filename = filename.replace('_inpaint', '')
    filename = decodeURIComponent(filename)

    return filename
  }
  catch (error) {
    console.error('解析URL时出错:', error)
    return ''
  }
}

/**
 * 获取当前目录（主图、详情页面、SKU图）
 * @returns 当前目录字符串
 */
export function getCurrentImageTypeString(type?: ImageType): string {
  switch (type) {
    case ImageType.MAIN:
      return '主图'
    case ImageType.DETAIL:
      return '详情页面'
    case ImageType.SKU:
      return 'SKU图'
    case ImageType.INSTALLATION:
      return '安装说明'
    default:
      return ''
  }
}

/**
 * 下载图片url为blob，返回blob对象
 * @param url 图片url
 * @returns blob对象
 */
export async function downloadImageAsBlob(url: string): Promise<Blob> {
  try {
    const response = await axiosInstance.get(url, {
      responseType: 'blob',
    })
    return response.data
  }
  catch (error) {
    console.error('下载图片失败:', error)
    throw error
  }
}

/**
 * 比较两个URL是否相同，忽略Expires和Signature参数
 * @param url1 第一个URL字符串
 * @param url2 第二个URL字符串
 * @returns 如果URL相同返回true，否则返回false
 */
export function compareUrls(url1: string, url2: string): boolean {
  const parsedUrl1 = new URL(url1)
  const parsedUrl2 = new URL(url2)

  // 比较协议、主机名、路径名和端口
  if (
    parsedUrl1.protocol !== parsedUrl2.protocol
    || parsedUrl1.hostname !== parsedUrl2.hostname
    || parsedUrl1.pathname !== parsedUrl2.pathname
    || parsedUrl1.port !== parsedUrl2.port
  ) {
    return false
  }

  // 获取查询参数
  const params1 = parsedUrl1.searchParams
  const params2 = parsedUrl2.searchParams

  params1.delete('Expires')
  params1.delete('Signature')
  params2.delete('Expires')
  params2.delete('Signature')

  const sortedParams1 = params1.toString().split('&').sort().join('&')
  const sortedParams2 = params2.toString().split('&').sort().join('&')

  // 比较其余参数
  return sortedParams1 === sortedParams2
}

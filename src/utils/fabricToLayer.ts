import type { fabric } from 'fabric'
import { type ImageLayer, type Layer, LayerType, type TextLayer } from '@/api/types'

/**
 * 将 fabric.js 对象转换为 Layer 类型
 * @param objects fabric.js 画布对象数组
 * @returns Layer[] 转换后的图层数组
 */
export function convertFabricObjectsToLayers(objects: fabric.Object[]): Layer[] {
  return objects
    .filter(obj => obj.type === 'rectWithText')
    .map((obj, index) => {
      if (obj.type === 'rectWithText') {
        const rectWithText = obj as any
        const textObj = rectWithText.text || {}

        const textLayer: TextLayer = {
          // BaseLayer 属性
          id: rectWithText.id || index,
          top: rectWithText.top || 0,
          left: rectWithText.left || 0,
          width: (rectWithText.width || 0) * (rectWithText.scaleX || 1),
          height: (rectWithText.height || 0) * (rectWithText.scaleY || 1),
          strokeWidth: rectWithText.strokeWidth || 0,
          stroke: rectWithText.stroke,
          borderRadius: rectWithText.data?.borderRadius || 0,
          fill: rectWithText.fill,
          visible: rectWithText.visible,
          label: 'text',
          remark: null,
          zIndex: index,
          // TextLayer 特有属性
          type: LayerType.TEXT,
          // width: (textObj.width || 0),
          // height: (textObj.height || 0),
          backgroundColor: rectWithText.fill || '',
          color: textObj.fill || '#000000',
          textAlign: 'left',
          letterSpacing: textObj.charSpacing || 0,
          fontFamily: textObj.fontFamily || '',
          fontSize: textObj.fontSize || 12,
          lineHeight: textObj.lineHeight || 1.16,
          direction: null,
          content: textObj.text || '',
          ocrContent: textObj.text || '',
          numlines: textObj.data?.numlines,
          isForeignLayer: rectWithText.data?.isForeignLayer,
        }
        return textLayer
      }
      else if (obj.type === 'image') {
        // 处理普通图片对象
        const imageLayer: ImageLayer = {
          // BaseLayer 属性
          id: (obj as any).id || index,
          top: obj.top || 0,
          left: obj.left || 0,
          width: (obj.width || 0) * (obj.scaleX || 1),
          height: (obj.height || 0) * (obj.scaleY || 1),
          label: 'image',
          remark: null,
          zIndex: index,
          // ImageLayer 特有属性
          type: LayerType.IMAGE,
          backgroundImg: (obj as fabric.Image).getSrc?.() || '',
          src: (obj as fabric.Image).getSrc?.() || '',
        }
        return imageLayer
      }
      return null
    })
    .filter(Boolean) as Layer[]
}

/**
 * get localStorage 获取本地存储
 * @param {string} key
 * @param { any } defaultValue 默认值
 */
export function getLocal(key: string, defaultValue?: any) {
  if (!key)
    throw new Error('key is empty')
  const value = localStorage.getItem(key)
  return value ? JSON.parse(value) : defaultValue
}

/**
 * set localStorage 设置本地存储
 * @param {string} key
 * @param value
 */
export function setLocal(key: string, value: unknown) {
  if (!key)
    throw new Error('key is empty')
  if (!value)
    return
  return localStorage.setItem(key, JSON.stringify(value))
}

/**
 * remove localStorage 移除某个本地存储
 * @param {string} key
 */
export function removeLocal(key: string) {
  if (!key)
    throw new Error('key is empty')
  return localStorage.removeItem(key)
}

/**
 * clear localStorage 清除本地存储
 */
export function clearLocal() {
  return localStorage.clear()
}

// @ts-nocheck
import { fabric } from 'fabric'

interface ExtendedTextbox extends fabric.Textbox {
  showTextBoxBorder?: boolean
  textboxBorderColor?: string
  textboxBorderWidth?: number
  textboxBorderRadius?: number
  __corner?: string
  parent?: any
  ignoreGuideline?: boolean
}

// 扩展 fabric 的类型定义
declare module 'fabric' {
  interface IObjectOptions {
    ignoreGuideline?: boolean
  }

  interface ITextboxOptions {
    ignoreGuideline?: boolean
  }

  interface IUtil {
    createClass<T>(parent: any, properties: any): { new(): T }
  }

  interface Static {
    RectWithText: { new(options: RectWithTextOptions): RectWithText }
  }
}

// 为 RectWithText 定义类型
export interface RectWithTextOptions extends fabric.IRectOptions {
  textOffsetLeft?: number
  textOffsetTop?: number
  _prevObjectStacking?: boolean
  _prevAngle?: number
  id?: string
  ignoreGuideline?: boolean
  borderRadius?: number
}

// 使用 type 来避免 initialize 方法的继承问题
export type RectWithText = fabric.Rect & {
  text: ExtendedTextbox
  textOffsetLeft: number
  textOffsetTop: number
  _prevObjectStacking: boolean | null
  _prevAngle: number
  strokeWidthUnscaled?: number
  borderRadius?: number
  data: any
  recalcTextPosition: () => void
  id: string | null
  initialize: (rectOptions: RectWithTextOptions, textOptions: fabric.ITextboxOptions, text: string) => void
  callSuper: any
  set: any
  toObject: (propertiesToInclude: string[]) => any
  setVerticalAlign: (align: 'top' | 'middle' | 'bottom') => void
}

// 绘制文本框边框的函数
// function drawTextboxBorder(ctx: CanvasRenderingContext2D, textbox: ExtendedTextbox) {
//   const width = textbox.width || 0
//   const height = textbox.calcTextHeight()
//   const borderWidth = (textbox.textboxBorderWidth ?? 0) / (textbox.scaleX || 1)
//   const borderColor = textbox.textboxBorderColor ?? 'transparent'
//   let borderRadius = textbox.textboxBorderRadius ?? 0
//   console.log('borderWidth', borderWidth, 'borderColor', borderColor, 'borderRadius', borderRadius);


//   if (borderWidth <= 0) {
//     return
//   }

//   // 缓存常用计算结果
//   const halfBorder = borderWidth / 2
//   const x = -width / 2 - halfBorder
//   const y = -height / 2 - halfBorder
//   const totalWidth = width + borderWidth
//   const totalHeight = height + borderWidth

//   // 确保边框半径不超过边界
//   borderRadius = Math.min(borderRadius, totalWidth / 2, totalHeight / 2)

//   ctx.save()
//   ctx.strokeStyle = borderColor
//   ctx.lineWidth = borderWidth
//   ctx.beginPath()

//   // 如果没有圆角，直接画矩形
//   if (borderRadius <= 0) {
//     ctx.strokeRect(x, y, totalWidth, totalHeight)
//   }
//   else {
//     // 使用arcTo绘制圆角矩形
//     ctx.moveTo(x + borderRadius, y)
//     ctx.lineTo(x + totalWidth - borderRadius, y)
//     ctx.arcTo(x + totalWidth, y, x + totalWidth, y + borderRadius, borderRadius)
//     ctx.lineTo(x + totalWidth, y + totalHeight - borderRadius)
//     ctx.arcTo(x + totalWidth, y + totalHeight, x + totalWidth - borderRadius, y + totalHeight, borderRadius)
//     ctx.lineTo(x + borderRadius, y + totalHeight)
//     ctx.arcTo(x, y + totalHeight, x, y + totalHeight - borderRadius, borderRadius)
//     ctx.lineTo(x, y + borderRadius)
//     ctx.arcTo(x, y, x + borderRadius, y, borderRadius)
//     ctx.closePath()
//     ctx.stroke()
//   }

//   ctx.restore()
// }

export function initFabricExtensions() {
  const originalRender = fabric.Textbox.prototype._render

  // 添加维持文字大小的功能
  // let lastHeight: number
  // fabric.Textbox.prototype.on('scaling', function (options: fabric.IEvent, this: ExtendedTextbox) {
  //   const controlPoint = this.__corner
  //   // mr和ml是水平缩放的控制点，不修改高度
  //   if (controlPoint && controlPoint !== 'mr' && controlPoint !== 'ml') {
  //     lastHeight = this.height! * (this.scaleY || 1)
  //   }

  //   // 重置scaleY并设置新的height
  //   this.set({
  //     height: lastHeight || this.height,
  //     scaleY: 1,
  //   })
  // })

  // 添加 RectWithText 类
  fabric.RectWithText = fabric.util.createClass(fabric.Rect, {
    type: 'rectWithText',
    text: null,
    textOffsetLeft: 0,
    textOffsetTop: 0,
    _prevObjectStacking: null,
    _prevAngle: 0,
    id: null,

    recalcTextPosition(this: RectWithText) {
      const sin = Math.sin(fabric.util.degreesToRadians(this.angle!))
      const cos = Math.cos(fabric.util.degreesToRadians(this.angle!))
      const newTop = sin * this.textOffsetLeft + cos * this.textOffsetTop
      const newLeft = cos * this.textOffsetLeft - sin * this.textOffsetTop
      const rectLeftTop = this.getPointByOrigin('left', 'top')
      this.text.set('left', rectLeftTop.x + newLeft)
      this.text.set('top', rectLeftTop.y + newTop)
    },

    initialize(this: RectWithText, rectOptions: RectWithTextOptions, textOptions: fabric.ITextboxOptions, text: string) {
      const padding = 0
      // 计算矩形的实际尺寸（padding）
      const originalWidth = rectOptions.width || 0
      const originalHeight = rectOptions.height || 0
      const width = originalWidth * (1 + padding)
      const height = originalHeight * (1 + padding)
      const left = (rectOptions.left || 0) - originalWidth * padding / 2
      const top = (rectOptions.top || 0) - originalHeight * padding / 2

      // 使用计算后的尺寸初始化矩形
      this.callSuper('initialize', {
        ...rectOptions,
        width,
        height,
        left,
        top,
      })
      this.id = rectOptions.id ?? null

      // 计算文本框的中心位置
      const rectCenterX = left + width / 2
      const rectCenterY = top + height / 2

      // 确保文本和矩形的可见性一致
      const isVisible = rectOptions.visible !== false
      const shouldIgnoreGuideline = rectOptions.ignoreGuideline === true || !isVisible

      // 存储原始边框宽度
      this.set('strokeWidthUnscaled', this.strokeWidth || 0)
      this.set('ignoreGuideline', shouldIgnoreGuideline)
      // 设置圆角属性
      this.set('borderRadius', rectOptions.borderRadius || 0)

      // 创建文本，使用原始宽度作为文本宽度
      this.text = new fabric.Textbox(text, {
        textAlign: 'left',
        ...textOptions,
        left: rectCenterX - originalWidth / 2,
        top: rectCenterY - (textOptions.fontSize || 20) / 2,
        width: originalWidth,
        selectable: false,
        evented: false,
        visible: isVisible,
        ignoreGuideline: shouldIgnoreGuideline,
      })

      // 设置文本对象的父引用
      this.text.parent = this

      // 计算文本相对于矩形的偏移量
      this.textOffsetLeft = this.text.left! - this.left!
      this.textOffsetTop = this.text.top! - this.top!

      this.on('moving', () => {
        this.recalcTextPosition()
      })

      this.on('rotating', () => {
        this.text.rotate((this.text.angle || 0) + (this.angle || 0) - this._prevAngle)
        this.recalcTextPosition()
        this._prevAngle = this.angle || 0
      })

      let isScaling = false

      this.on('scaling', () => {
        if (!isScaling) {
          isScaling = true
        }

        // 获取矩形的实际宽度（考虑缩放）
        const actualWidth = (this.width || 0) * (this.scaleX || 1)
        // 设置文本宽度为矩形宽度减去边距
        this.text.set({
          width: actualWidth - actualWidth * padding / 2 * this.scaleX,
        })

        // 调整边框宽度以保持视觉上的一致性
        const strokeWidthUnscaled = this.get('strokeWidthUnscaled') || 0
        // 使用 scaleX 和 scaleY 的最小值来调整 strokeWidth
        const scale = Math.min(this.scaleX || 1, this.scaleY || 1)
        this.set('strokeWidth', strokeWidthUnscaled / scale)

        this.recalcTextPosition()
      })

      this.on('mouseup', () => {
        if (isScaling) {
          // 计算新的实际尺寸
          const newWidth = (this.width || 0) * (this.scaleX || 1)
          const newHeight = (this.height || 0) * (this.scaleY || 1)
          const strokeWidth = this.get('strokeWidthUnscaled') || 0

          // 重置对象属性
          this.set({
            width: newWidth,
            height: newHeight,
            scaleX: 1,
            scaleY: 1,
            strokeWidth,
          })

          // 更新文本宽度
          this.text.set({
            width: newWidth - newWidth * padding / 2 * this.scaleX,
          })

          // 重新计算文本位置
          this.recalcTextPosition()

          isScaling = false
        }
      })

      this.on('added', () => {
        this.canvas?.add(this.text)
      })
      this.on('removed', () => {
        this.canvas?.remove(this.text)
      })
      this.on('mousedown:before', () => {
        this._prevObjectStacking = this.canvas?.preserveObjectStacking || null
        if (this.canvas) {
          this.canvas.preserveObjectStacking = true
        }
      })
      this.on('mousedblclick', () => {
        this.text.selectable = true
        this.text.evented = true
        this.canvas?.setActiveObject(this.text)
        this.text.enterEditing()
        this.selectable = false
      })
      this.on('deselected', () => {
        if (this.canvas) {
          this.canvas.preserveObjectStacking = this._prevObjectStacking || false
        }
      })
      this.text.on('editing:exited', () => {
        this.text.selectable = false
        this.text.evented = false
        this.selectable = true
      })
    },

    // 添加设置垂直对齐的方法
    setVerticalAlign(this: RectWithText, align: 'top' | 'middle' | 'bottom') {
      const rectHeight = (this.height || 0) * (this.scaleY || 1)
      const textHeight = this.text.calcTextHeight()
      let newTop = 0

      switch (align) {
        case 'top':
          newTop = 10 // 顶部留出10px的padding
          break
        case 'middle':
          newTop = (rectHeight - textHeight) / 2
          break
        case 'bottom':
          newTop = rectHeight - textHeight - 10 // 底部留出10px的padding
          break
      }

      // 更新文本的垂直位置
      this.textOffsetTop = newTop
      this.recalcTextPosition()
      this.canvas?.requestRenderAll()
    },

    // 添加 toObject 方法来确保所有属性都被序列化
    toObject(propertiesToInclude: string[] = []) {
      return fabric.util.object.extend(this.callSuper('toObject', propertiesToInclude), {
        text: this.text.toObject(),
        textOffsetLeft: this.textOffsetLeft,
        textOffsetTop: this.textOffsetTop,
        _prevAngle: this._prevAngle,
        strokeWidthUnscaled: this.strokeWidthUnscaled,
        borderRadius: this.borderRadius,
        data: this.data,
        id: this.id,
      });
    },
  }) as unknown as { new(rectOptions: RectWithTextOptions, textOptions: fabric.ITextboxOptions, text: string): RectWithText }

  fabric.RectWithText.fromObject = function (object: any, callback: (obj: RectWithText) => void) {
    // 先创建文本对象
    fabric.Textbox.fromObject(object.text, function (textbox) {
      // 分离矩形属性和文本属性
      const { text: textObject, ...rectOptions } = object

      // 创建基本的 RectWithText 实例
      const rect = new fabric.RectWithText(
        rectOptions,
        textbox,  // 使用已创建的 textbox 对象
        textbox.text || ''
      )

      // 使用 set 恢复所有属性
      rect.set(object)

      // 确保文本对象被正确设置
      rect.text = textbox
      rect.text.parent = rect

      // 重新计算文本位置
      rect.recalcTextPosition()

      callback(rect)
    })
  }

  fabric.Textbox.prototype._render = function (ctx) {
    const thisTextbox = this as ExtendedTextbox

    originalRender.call(thisTextbox, ctx)

    // drawTextboxBorder(ctx, thisTextbox)
  }

  const customProperties = ['showTextBoxBorder', 'textboxBorderColor', 'textboxBorderWidth', 'textboxBorderRadius']
  fabric.Textbox.prototype.cacheProperties = Array.from(new Set([
    ...(fabric.Textbox.prototype.cacheProperties || []),
    ...customProperties,
  ]))

  fabric.RectWithText.prototype.render = function (ctx: CanvasRenderingContext2D) {
    if (!this.visible) return

    ctx.save()

    // 变换坐标系
    const m = this.calcTransformMatrix()
    ctx.transform(m[0], m[1], m[2], m[3], m[4], m[5])

    // 计算实际的线宽（考虑缩放）
    const scaleX = Math.sqrt(m[0] * m[0] + m[1] * m[1])
    const scaleY = Math.sqrt(m[2] * m[2] + m[3] * m[3])
    const strokeWidth = this.strokeWidth ? this.strokeWidth / ((scaleX + scaleY) / 2) : 0

    // 绘制圆角矩形
    const w = this.width!
    const h = this.height!
    // 圆角半径也需要考虑缩放
    const baseRadius = this.data.borderRadius || 0
    const r = Math.min(baseRadius / ((scaleX + scaleY) / 2), w / 2, h / 2)

    // 将原点移动到矩形的左上角
    const x = -w / 2
    const y = -h / 2

    ctx.beginPath()

    // Squircle 算法的控制点比例
    const magic = 0.552284749831
    const cornerRadius = r
    const cornerControl = cornerRadius * magic

    // 从左上角开始，顺时针绘制
    ctx.moveTo(x + cornerRadius, y)

    // 上边
    ctx.lineTo(x + w - cornerRadius, y)

    // 右上角
    ctx.bezierCurveTo(
      x + w - cornerRadius + cornerControl, y,
      x + w, y + cornerRadius - cornerControl,
      x + w, y + cornerRadius
    )

    // 右边
    ctx.lineTo(x + w, y + h - cornerRadius)

    // 右下角
    ctx.bezierCurveTo(
      x + w, y + h - cornerRadius + cornerControl,
      x + w - cornerRadius + cornerControl, y + h,
      x + w - cornerRadius, y + h
    )

    // 下边
    ctx.lineTo(x + cornerRadius, y + h)

    // 左下角
    ctx.bezierCurveTo(
      x + cornerRadius - cornerControl, y + h,
      x, y + h - cornerRadius + cornerControl,
      x, y + h - cornerRadius
    )

    // 左边
    ctx.lineTo(x, y + cornerRadius)

    // 左上角
    ctx.bezierCurveTo(
      x, y + cornerRadius - cornerControl,
      x + cornerRadius - cornerControl, y,
      x + cornerRadius, y
    )

    ctx.closePath()

    // 填充
    if (this.fill) {
      ctx.fillStyle = this.fill as string
      ctx.fill()
    }

    // 描边
    if (this.stroke && strokeWidth) {
      ctx.strokeStyle = this.stroke
      ctx.lineWidth = strokeWidth
      ctx.stroke()
    }

    ctx.restore()
  }
}

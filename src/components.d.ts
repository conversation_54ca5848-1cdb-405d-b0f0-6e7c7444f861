/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AITranslateDialog: typeof import('./components/AITranslateDialog/AITranslateDialog.vue')['default']
    ContentSection: typeof import('./components/ContentSection/ContentSection.vue')['default']
    DesignThumbnailList: typeof import('./components/DesignThumbnailList/DesignThumbnailList.vue')['default']
    DesignUploadDialog: typeof import('./components/DesignUploadDialog/DesignUploadDialog.vue')['default']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElButtonGroup: typeof import('element-plus/es')['ElButtonGroup']
    ElCard: typeof import('element-plus/es')['ElCard']
    ElCol: typeof import('element-plus/es')['ElCol']
    ElColorPicker: typeof import('element-plus/es')['ElColorPicker']
    ElConfigProvider: typeof import('element-plus/es')['ElConfigProvider']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElLink: typeof import('element-plus/es')['ElLink']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElPopover: typeof import('element-plus/es')['ElPopover']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioButton: typeof import('element-plus/es')['ElRadioButton']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElRow: typeof import('element-plus/es')['ElRow']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTabPane: typeof import('element-plus/es')['ElTabPane']
    ElTabs: typeof import('element-plus/es')['ElTabs']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    ElUpload: typeof import('element-plus/es')['ElUpload']
    Header: typeof import('./components/Layout/Header.vue')['default']
    IconCommunity: typeof import('./components/icons/IconCommunity.vue')['default']
    IconDocumentation: typeof import('./components/icons/IconDocumentation.vue')['default']
    IconEcosystem: typeof import('./components/icons/IconEcosystem.vue')['default']
    IconSupport: typeof import('./components/icons/IconSupport.vue')['default']
    IconTooling: typeof import('./components/icons/IconTooling.vue')['default']
    ImageTag: typeof import('./components/ImageTag/ImageTag.vue')['default']
    Layout: typeof import('./components/Layout/Layout.vue')['default']
    MarkerList: typeof import('./components/MarkerList/MarkerList.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    ThumbnailList: typeof import('./components/ThumbnailList/ThumbnailList.vue')['default']
    ThumbnailPreviewList: typeof import('./components/ThumbnailPreviewList/ThumbnailPreviewList.vue')['default']
  }
  export interface ComponentCustomProperties {
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}

import type { UserInfo, UserLoginReq } from '@/api/auth'
import { login, PermissionsEnum } from '@/api/auth'
import { defineStore } from 'pinia'

interface PageInfo {
  key: string
  path: string
}

export const useUserStore = defineStore('user', () => {
  // 用户信息
  const userInfo = ref<UserInfo | null>(null)

  // 可访问的页面
  const accessiblePages = computed<PageInfo[]>(() => {
    if (!userInfo.value)
      return []

    const pages: PageInfo[] = []
    const permissions = userInfo.value.permissionCodes || []

    if (permissions.includes(PermissionsEnum.TRANSLATE) || permissions.includes(PermissionsEnum.TRANSLATE_REVIEW))
      pages.push({ key: 'translate', path: '/translate' })

    if (permissions.includes(PermissionsEnum.DESIGN))
      pages.push({ key: 'design', path: '/design' })

    if (permissions.includes(PermissionsEnum.DESIGN_REVIEW))
      pages.push({ key: 'design_check', path: '/design-check' })

    return pages
  })

  // 登录
  const loginAction = async (loginData: UserLoginReq) => {
    const res = await login(loginData)
    if (res.success) {
      userInfo.value = res.data
      return true
    }
    else {
      ElMessage.error(res.message)
    }
    return false
  }
  // 登出
  const logout = async () => {
    userInfo.value = null
    localStorage.removeItem('user-storage')
  }

  // 获取用户默认页面
  const getDefaultPage = (): string => {
    if (!userInfo.value)
      return '/login'

    const isTranslator = userInfo.value.permissionCodes.includes(PermissionsEnum.TRANSLATE)
      || userInfo.value.permissionCodes.includes(PermissionsEnum.TRANSLATE_REVIEW)
    const isDesigner = userInfo.value.permissionCodes.includes(PermissionsEnum.DESIGN)
    const isDesignerValidator = userInfo.value.permissionCodes.includes(PermissionsEnum.DESIGN_REVIEW)

    if (isTranslator) {
      return '/translate'
    }
    else if (isDesigner) {
      return '/design'
    }
    else if (isDesignerValidator) {
      return '/design-check'
    }
    else {
      return '/login'
    }
  }

  return {
    userInfo,
    loginAction,
    logout,
    getDefaultPage,
    accessiblePages,
  }
}, {
  persist: {
    key: 'user-storage',
    storage: localStorage,
  },
})

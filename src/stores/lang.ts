import type { Language } from 'element-plus/es/locale'
import { getLocal, setLocal } from '@/utils/local'
import ja from 'element-plus/es/locale/lang/ja'
import zhCn from 'element-plus/es/locale/lang/zh-cn'
import { defineStore } from 'pinia'
import { useI18n } from 'vue-i18n'

function getBrowserLang(): 'zh' | 'ja' {
  const lang = navigator.language.toLowerCase()
  return lang.includes('ja') ? 'ja' : 'zh'
}

export const useLangStore = defineStore('lang', () => {
  const savedLang = getLocal('lang', getBrowserLang())
  const currentElpLang = ref<Language>(savedLang === 'ja' ? ja : zhCn)
  const currentLang = ref<string>(savedLang)
  const { locale } = useI18n()

  const changeLang = (lang: 'zh' | 'ja') => {
    currentElpLang.value = lang === 'zh' ? zhCn : ja
    currentLang.value = lang
    setLocal('lang', lang)
    locale.value = lang
  }

  return {
    currentElpLang,
    currentLang,
    changeLang,
  }
})

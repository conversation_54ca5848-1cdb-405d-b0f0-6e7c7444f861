import type { ImageData, ImageInfoExtend } from '@/api/types'
import { listImages, pollingTranslation, TranslateStatus } from '@/api/imageTranslationApi'
import { ImageType } from '@/api/types'
import { compareUrls, downloadImageAsBlob, extractOssFilename } from '@/utils/utils'
import { defineStore } from 'pinia'
import { useRoute } from 'vue-router'

export const useImagesStore = defineStore('images', () => {
  const route = useRoute()
  // 存储图片列表
  const images = reactive<ImageInfoExtend[]>([])
  const transId = ref('')
  const pollingInterval = ref<number | null>(null)
  const isPolling = ref(false)
  const activeImageId = ref('0')
  const hasFirstPollCompleted = ref(false)
  const currentActiveImage = computed(() => images.find(img => img.id === activeImageId.value))

  // 计算缩略图的整体显示状态
  const isThumbnailsReady = computed(() => hasFirstPollCompleted.value)

  // 判断单个缩略图是否解析失败
  const isThumbnailFailed = (imageId: string) => {
    const image = images.find(img => img.id === imageId)
    return image?.status === TranslateStatus.FAILED || image?.status === TranslateStatus.INVALID_SIZE
  }

  const currentImageType = ref<ImageType | undefined>(undefined)

  // 获取当前transId，优先从URL中获取
  const getCurrentTransId = computed(() => {
    const urlTransId = route.query.transId
    // 使用可选链和类型断言来确保类型安全
    return (urlTransId as string | undefined) ?? transId.value
  })

  const getListImages = async (transId: string, type: ImageType) => {
    const imageList = await listImages(transId, type)
    // 处理新获取的图片列表
    if (!imageList.data?.length)
      return

    // 创建一个映射来存储现有图片的索引
    const existingImagesMap = new Map(
      images.map((img, index) => [img.id, index]),
    )

    // 处理新的图片列表，保持API返回的顺序
    const updatedImages = imageList.data.map(newImg => ({ ...newImg, type }))

    // 保持顺序
    updatedImages.forEach((img) => {
      const existingIndex = existingImagesMap.get(img.id)
      if (existingIndex !== undefined) {
        images[existingIndex] = img
      }
      else {
        // 如果是新图片，添加到列表末尾
        images.push(img)
      }
    })
  }

  const getAllImages = async (id?: string) => {
    // 清空现有图片
    images.splice(0)
    activeImageId.value = '0' // 重置 activeImageId

    const currentTransId = id ?? getCurrentTransId.value
    if (currentTransId === '-1')
      return

    // 更新 store 中的 transId
    transId.value = currentTransId

    // 获取所有类型的图片
    await Promise.all([
      getListImages(currentTransId, ImageType.MAIN),
      getListImages(currentTransId, ImageType.DETAIL),
      getListImages(currentTransId, ImageType.SKU),
      getListImages(currentTransId, ImageType.INSTALLATION),
    ])

    // 设置默认选中第一张图片
    if (images.length > 0) {
      const mainImages = images.filter(img => img.type === ImageType.MAIN)
      if (mainImages.length > 0 && (activeImageId.value === '0' || !activeImageId.value)) {
        activeImageId.value = mainImages[0].id
      }
      else if (activeImageId.value === '0' || !activeImageId.value) {
        activeImageId.value = images[0].id
      }
    }
  }

  // 更新图片翻译状态
  const updateImageTranslations = (imageTranslations: ImageData[]) => {
    imageTranslations.forEach(async (translation) => {
      const image = images.find(img => img.id === translation.imageId)
      if (!image) {
        return
      }
      image.status = translation.status
      image.layers = translation.layers || []
      image.originalLayers = JSON.parse(JSON.stringify(translation.layers))
      image.backgroundImg = translation.inPaintingImage
      // 异步加载背景图片并转换为Blob，同时保存文件名
      if (translation.inPaintingImage && image.status === TranslateStatus.SUCCESS) {
        if (!image.backgroundImgBlob || !compareUrls(image.backgroundImg, translation.inPaintingImage)) {
          image.backgroundImgFilename = decodeURIComponent(extractOssFilename(image.backgroundImg))
          image.backgroundImgBlob = await downloadImageAsBlob(translation.inPaintingImage)
        }
      }
      if (translation.status === TranslateStatus.SUCCESS && (activeImageId.value === '0' || !activeImageId.value) && images[0]?.id === translation.imageId) {
        activeImageId.value = translation.imageId
      }
    })
  }

  // 下载原图blob
  const downloadOriginalImage = async (imageId: string) => {
    const image = images.find(img => img.id === imageId)
    if (!image || !image.image)
      return

    try {
      const response = await fetch(image.image)
      const blob = await response.blob()
      image.originalImageBlob = blob
    }
    catch (error) {
      console.error('Failed to download original image:', error)
    }
  }

  // 下载所有原图blob
  const downloadAllOriginalImages = async () => {
    const downloadPromises = images.map(img => downloadOriginalImage(img.id))
    await Promise.all(downloadPromises)
  }

  // 停止轮询
  const stopPolling = () => {
    if (pollingInterval.value) {
      window.clearInterval(pollingInterval.value)
      pollingInterval.value = null
    }
    isPolling.value = false
  }

  // 开始轮询翻译结果
  const startPolling = (id?: string, imageType: ImageType = ImageType.MAIN): Promise<void> => {
    return new Promise((resolve) => {
      if (isPolling.value)
        return resolve()

      const currentTransId = id ?? getCurrentTransId.value
      if (currentTransId === '-1')
        return resolve()

      transId.value = currentTransId
      isPolling.value = true
      hasFirstPollCompleted.value = false

      const poll = async () => {
        try {
          const response = await pollingTranslation(currentTransId, imageType)
          if (!response.data)
            return

          updateImageTranslations(response.data?.imageTranslations || [])

          if (response.data.done) {
            hasFirstPollCompleted.value = true
            stopPolling()
            resolve()
          }
        }
        catch (error) {
          console.error('轮询出错:', error)
          stopPolling()
          resolve()
        }
      }

      poll()
      if (isPolling.value) {
        pollingInterval.value = window.setInterval(poll, 3000)
      }
    })
  }

  // 切换到上一张同类型图片
  const prevImage = (type: ImageType) => {
    const sameTypeImages = images.filter(img => img.type === type)
    if (sameTypeImages.length <= 1)
      return

    const currentIndex = sameTypeImages.findIndex(img => img.id === activeImageId.value)
    if (currentIndex === -1)
      return

    const prevIndex = currentIndex === 0 ? sameTypeImages.length - 1 : currentIndex - 1
    activeImageId.value = sameTypeImages[prevIndex].id
  }

  // 切换到下一张同类型图片
  const nextImage = (type: ImageType) => {
    const sameTypeImages = images.filter(img => img.type === type)
    if (sameTypeImages.length <= 1)
      return

    const currentIndex = sameTypeImages.findIndex(img => img.id === activeImageId.value)
    if (currentIndex === -1)
      return

    const nextIndex = currentIndex === sameTypeImages.length - 1 ? 0 : currentIndex + 1
    activeImageId.value = sameTypeImages[nextIndex].id
  }

  // 切换图片类型时，设置该类型的第一张图片为活动图片
  const switchImageType = (type: ImageType) => {
    const imagesOfType = images.filter(img => img.type === type)
    if (imagesOfType.length > 0) {
      activeImageId.value = imagesOfType[0].id
    }
    else {
      activeImageId.value = '0'
    }
  }

  // 初始化store
  const init = async () => {
    const currentTransId = getCurrentTransId.value
    if (currentTransId !== '-1') {
      await getAllImages(currentTransId)
      startPolling(currentTransId)
    }
  }

  // 在组件卸载时清理轮询
  onUnmounted(() => {
    stopPolling()
  })

  // 更新图片的原始图层数据
  const updateOriginalLayers = (imageId: string) => {
    const image = images.find(img => img.id === imageId)
    if (image && image.layers) {
      image.originalLayers = JSON.parse(JSON.stringify(image.layers))
    }
  }

  // 检查所有图片是否都已成功处理完成
  const isAllImagesProcessed = computed(() => {
    if (images.length === 0)
      return false
    return images.every(img => img.status === TranslateStatus.SUCCESS)
  })

  return {
    images,
    transId,
    activeImageId,
    currentActiveImage,
    isPolling,
    hasFirstPollCompleted,
    getCurrentTransId,
    getAllImages,
    updateImageTranslations,
    startPolling,
    stopPolling,
    prevImage,
    nextImage,
    init,
    switchImageType,
    updateOriginalLayers,
    isThumbnailsReady,
    isAllImagesProcessed,
    isThumbnailFailed,
    currentImageType,
    downloadOriginalImage,
    downloadAllOriginalImages,
  }
})

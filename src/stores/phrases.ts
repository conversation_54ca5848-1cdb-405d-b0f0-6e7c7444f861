import type { Phrase } from '@/api/types'
import { listPhrases } from '@/api/imageTranslationApi'
import { defineStore } from 'pinia'
import { ref } from 'vue'

export const usePhrasesStore = defineStore('phrases', () => {
  const phrases = ref<Phrase[]>([])
  const isLoaded = ref(false)

  async function fetchPhrases() {
    if (isLoaded.value && phrases.value.length > 0)
      return

    const res = await listPhrases()
    if (res.success) {
      phrases.value = res.data
      isLoaded.value = true
    }
  }

  function clearPhrases() {
    phrases.value = []
    isLoaded.value = false
  }

  return {
    phrases,
    isLoaded,
    fetchPhrases,
    clearPhrases,
  }
})

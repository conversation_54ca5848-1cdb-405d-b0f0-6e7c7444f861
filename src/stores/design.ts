import { queryDesign } from '@/api/imageTranslationApi'
import { type DesignInfoVO, ImageType } from '@/api/types'
import { defineStore } from 'pinia'
import { useImagesStore } from './images'

export const useDesignStore = defineStore('design', () => {
  const designInfoMap = reactive<Record<ImageType, DesignInfoVO[]>>({
    [ImageType.MAIN]: [],
    [ImageType.DETAIL]: [],
    [ImageType.SKU]: [],
    [ImageType.INSTALLATION]: [],
  })

  const isLoading = reactive<Record<ImageType, boolean>>({
    [ImageType.MAIN]: false,
    [ImageType.DETAIL]: false,
    [ImageType.SKU]: false,
    [ImageType.INSTALLATION]: false,
  })
  const isFetchingAllTypes = ref(false)

  const thumbnailUrls = reactive<Record<string, string>>({})
  const isLoadingImages = reactive<Record<string, boolean>>({})
  const currentDesignIndex = reactive<Record<ImageType, number>>({
    [ImageType.MAIN]: 0,
    [ImageType.DETAIL]: 0,
    [ImageType.SKU]: 0,
    [ImageType.INSTALLATION]: 0,
  })

  async function fetchDesignInfo(type: ImageType, transId: string) {
    if (isLoading[type]) {
      return
    }
    isLoading[type] = true
    try {
      const response = await queryDesign(transId, type)
      if (response.success) {
        designInfoMap[type] = response.data || []
        loadDesignImages(type) // Load images for this specific type
      }
      else {
        designInfoMap[type] = []
        ElMessage.error(response.message || `获取设计稿(${type})信息失败`)
      }
    }
    catch (error) {
      designInfoMap[type] = []
      console.error(`Error fetching design info for type ${type}:`, error)
      ElMessage.error(`获取设计稿(${type})信息失败`)
    }
    finally {
      isLoading[type] = false
    }
  }

  async function fetchAllDesignInfoTypes() {
    const imagesStore = useImagesStore()
    const currentTransId = imagesStore.getCurrentTransId
    if (!currentTransId || currentTransId === '-1') {
      clearAllDesignInfo()
      return
    }
    if (isFetchingAllTypes.value) {
      return
    }

    isFetchingAllTypes.value = true
    Object.keys(isLoading).forEach(key => isLoading[key as ImageType] = false)

    const typesToFetch: ImageType[] = [ImageType.MAIN, ImageType.DETAIL, ImageType.SKU, ImageType.INSTALLATION]
    const fetchPromises = typesToFetch.map(type => fetchDesignInfo(type, currentTransId))

    try {
      await Promise.allSettled(fetchPromises)
      await loadAllDesignImages() // Load all images after fetching info
    }
    catch (error) {
      console.error('fetchAllDesignInfoTypes: Error during fetching all types:', error)
    }
    finally {
      isFetchingAllTypes.value = false
    }
  }

  async function loadDesignImages(type: ImageType) {
    const designs = designInfoMap[type] || []
    const loadPromises = designs.map((design) => {
      if (design.designId && design.designLink && !(design.designId in thumbnailUrls)) {
        return new Promise<void>((resolve) => {
          isLoadingImages[design.designId!] = true
          thumbnailUrls[design.designId!] = 'loading' // Mark as loading
          const img = new Image()
          img.crossOrigin = 'anonymous' // 添加跨域支持
          img.onload = () => {
            thumbnailUrls[design.designId!] = design.designLink!
            isLoadingImages[design.designId!] = false
            resolve()
          }
          img.onerror = (error) => {
            console.error(`Failed to load design image ${design.designId}:`, error)
            thumbnailUrls[design.designId!] = '' // Placeholder or empty
            isLoadingImages[design.designId!] = false
            resolve() // Resolve even on error
          }
          img.src = design.designLink!
        }).catch(err => console.error('Image loading promise failed:', err))
      }
      return Promise.resolve()
    })
    await Promise.allSettled(loadPromises)
  }

  async function loadAllDesignImages() {
    const allDesigns = Object.values(designInfoMap).flat()
    const loadPromises = allDesigns.map((design) => {
      if (design.designId && design.designLink && !(design.designId in thumbnailUrls)) {
        return new Promise<void>((resolve) => {
          isLoadingImages[design.designId!] = true
          thumbnailUrls[design.designId!] = 'loading'
          const img = new Image()
          img.crossOrigin = 'anonymous' // 添加跨域支持
          img.onload = () => {
            thumbnailUrls[design.designId!] = design.designLink!
            isLoadingImages[design.designId!] = false
            resolve()
          }
          img.onerror = (error) => {
            console.error(`Failed to preload design image ${design.designId}:`, error)
            thumbnailUrls[design.designId!] = ''
            isLoadingImages[design.designId!] = false
            resolve()
          }
          img.src = design.designLink!
        }).catch(err => console.error('Image loading promise failed:', err))
      }
      return Promise.resolve()
    })

    try {
      await Promise.allSettled(loadPromises)
    }
    catch (error) {
      console.error('loadAllDesignImages: Error during preloading:', error)
    }
  }

  function selectDesign(designId: string) {
    if (designId) {
      const imagesStore = useImagesStore()
      imagesStore.activeImageId = designId

      let foundType: ImageType | null = null
      let foundIndex = -1

      for (const type in designInfoMap) {
        const typeKey = type as ImageType
        const designIndex = designInfoMap[typeKey].findIndex(d => d.designId === designId)
        if (designIndex !== -1) {
          foundType = typeKey
          foundIndex = designIndex
          break
        }
      }
      if (foundType !== null && foundIndex !== -1) {
        currentDesignIndex[foundType] = foundIndex
      }
    }
  }

  function prevDesign(type: ImageType) {
    const designs = designInfoMap[type] || []
    if (designs.length <= 1)
      return null
    const prevIndex = currentDesignIndex[type] <= 0 ? designs.length - 1 : currentDesignIndex[type] - 1
    currentDesignIndex[type] = prevIndex
    const design = designs[prevIndex]
    if (design && design.designId)
      return design.designId
    return null
  }

  function nextDesign(type: ImageType) {
    const designs = designInfoMap[type] || []
    if (designs.length <= 1)
      return null
    const nextIndex = currentDesignIndex[type] >= designs.length - 1 ? 0 : currentDesignIndex[type] + 1
    currentDesignIndex[type] = nextIndex
    const design = designs[nextIndex]
    if (design && design.designId)
      return design.designId
    return null
  }

  const getDesignInfoByType = (type: ImageType) => computed(() => designInfoMap[type] || [])
  const getLoadingStateByType = (type: ImageType) => computed(() => isLoading[type])
  const getIsFetchingAllTypes = computed(() => isFetchingAllTypes.value)
  const getDesignImageUrl = (designId: string) => thumbnailUrls[designId] || ''
  const isDesignImageLoading = (designId: string) => !!isLoadingImages[designId]

  function clearAllDesignInfo() {
    Object.keys(designInfoMap).forEach(key => designInfoMap[key as ImageType] = [])
    Object.keys(thumbnailUrls).forEach(key => delete thumbnailUrls[key])
    Object.keys(isLoadingImages).forEach(key => delete isLoadingImages[key])
  }

  return {
    designInfoMap,
    isLoading,
    isFetchingAllTypes,
    thumbnailUrls,
    isLoadingImages,
    currentDesignIndex,
    fetchDesignInfo,
    fetchAllDesignInfoTypes,
    loadAllDesignImages,
    selectDesign,
    prevDesign,
    nextDesign,
    clearAllDesignInfo,
    getDesignInfoByType,
    getLoadingStateByType,
    getIsFetchingAllTypes,
    getDesignImageUrl,
    isDesignImageLoading,
  }
})

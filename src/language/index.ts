import { getLocal, setLocal } from '@/utils/local'
import { createI18n } from 'vue-i18n'
import JA from './ja.json'
import CN from './zh.json'

function getLocalLang() {
  let localLang = getLocal('lang')
  if (!localLang) {
    let defaultLang = navigator.language
    if (defaultLang) {
      defaultLang = defaultLang.split('-')[0]

      localLang = defaultLang.split('-')[0]
    }
    setLocal('lang', defaultLang)
  }
  return localLang
}
const lang = getLocalLang()

const i18n = createI18n({
  allowComposition: true,
  globalInjection: true,
  fallbackLocale: 'zh',
  legacy: false,
  locale: lang,
  messages: {
    ja: JA,
    zh: CN,
  },
})

export default i18n
export function t(key: any) {
  return i18n.global.t(key)
}

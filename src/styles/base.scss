html {
  font-weight: 100;
  overflow-y: hidden;
  font-family: sans-serif;
  min-width: 800px;
}

html:lang(zh),
html:lang(zh-CN),
html:lang(zh-<PERSON>) {
  font-family: -apple-system, 'PingFang SC', 'Microsoft YaHei', 'Hiragino Sans GB', 'WenQuanYi Micro Hei', sans-serif;
}

html:lang(ja) {
  font-family: 'Hiragino Kaku Gothic ProN', 'Yu Gothic', 'Meiryo', sans-serif;
}

html,
body,
#app {
  height: 100%;
  width: 100%;
  font-weight: 400;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
  font-size: 12px;
}

p {
  margin: 0;
}

a {
  text-decoration: none;
  color: #399e96;
}

img {
  vertical-align: middle;
  border-style: none;
}

* {
  box-sizing: border-box;
}

ul {
  margin: 0;
  padding: 0;
  list-style: none;
}

/////// 滚动条重置 ///////
::-webkit-scrollbar {
  width: 11px;
  height: 11px;
}

::-webkit-scrollbar-track {
  background-color: #f9f9f9;
}

::-webkit-scrollbar-thumb {
  background-color: #c2c3c9;
  // border-radius: 5px;
  border: 1px solid #f1f1f1;
  -webkit-transition: background-color 0.3s;
  transition: background-color 0.3s;
}

::-webkit-scrollbar-thumb:active,
::-webkit-scrollbar-thumb:hover {
  background-color: #686868;
}

::-webkit-scrollbar-corner {
  background-color: #fff;
}

input:-webkit-autofill {
  background-color: transparent !important;
  -webkit-box-shadow: 0 0 0px 1000px white inset !important;
  transition: background-color 5000s ease-in-out 0s;
}

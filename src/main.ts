import i18n from '@/language'
import { createPinia } from 'pinia'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'

import { createApp } from 'vue'

import App from './App.vue'

import router from './router'

import '@/permission'

import './styles/index.scss'
import '@/assets/iconfont/iconfont.css'

async function bootstrap() {
  const app = createApp(App)

  const pinia = createPinia()
  pinia.use(piniaPluginPersistedstate)
  app.use(pinia)
  app.use(router)
  app.use(i18n)

  app.mount('#app')
}

bootstrap()

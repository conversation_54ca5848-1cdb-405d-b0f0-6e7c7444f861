import { PermissionsEnum } from '@/api/auth'
import router from '@/router'
import { useUserStore } from '@/stores/user'
import { jwtDecode } from 'jwt-decode'

interface GaiaJwtPayload {
  jti: string
  user: string | {
    userId: number
    userName: string
    permissionCodes: string[]
  }
}

const whiteList = ['/login']

// 定义路由权限映射
const routePermissionMap = {
  '/translate': [PermissionsEnum.TRANSLATE, PermissionsEnum.TRANSLATE_REVIEW],
  '/design': [PermissionsEnum.DESIGN],
  '/design-check': [PermissionsEnum.DESIGN_REVIEW],
}

// 获取cookie中的token
function getTokenFromCookie(cookieName: string): string | null {
  const cookies = document.cookie.split(';')
  for (let i = 0; i < cookies.length; i++) {
    const cookie = cookies[i].trim()
    if (cookie.startsWith(`${cookieName}=`)) {
      return cookie.substring(cookieName.length + 1)
    }
  }
  return null
}

// 解析JWT token获取用户信息
function getUserFromToken(token: string) {
  try {
    const decoded = jwtDecode<GaiaJwtPayload>(token)
    if (decoded.user) {
      // 用户信息可能是字符串形式，需要解析
      const userInfo = typeof decoded.user === 'string'
        ? JSON.parse(decoded.user)
        : decoded.user

      return {
        userName: userInfo.userName,
        userId: userInfo.userId,
        name: userInfo.userName,
        jwt: token,
        permissionCodes: userInfo.permissionCodes || [],
      }
    }
    return null
  }
  catch (error) {
    console.error('JWT解析失败:', error)
    return null
  }
}

router.beforeEach(async (to, from, next) => {
  const userStore = useUserStore()
  let hasUserInfo = userStore.userInfo?.userName
  let token = userStore.userInfo?.jwt

  // 如果本地没有用户信息，尝试从cookie获取token
  if ((!hasUserInfo || !token) && !whiteList.includes(to.path)) {
    const gaiaToken = getTokenFromCookie('GAIA_TOKEN')
    if (gaiaToken) {
      const userInfo = getUserFromToken(gaiaToken)
      if (userInfo) {
        // 设置用户信息到store
        userStore.userInfo = userInfo
        hasUserInfo = userInfo.userName
        token = gaiaToken
      }
    }
  }

  if (hasUserInfo && token) {
    if (to.path === '/login') {
      next({ path: '/' })
    }
    else {
      // 检查路由权限
      const requiredPermissions = routePermissionMap[to.path as keyof typeof routePermissionMap]
      if (!requiredPermissions) {
        // 如果路由没有配置权限要求，允许访问
        next()
        return
      }

      const userPermissions = userStore.userInfo?.permissionCodes || []
      const hasPermission = requiredPermissions.some(permission =>
        userPermissions.includes(permission),
      )

      if (hasPermission) {
        next()
      }
      else {
        next('/upload')
      }
    }
  }
  else if (whiteList.includes(to.path)) {
    next()
  }
  else {
    next('/login')
  }
})

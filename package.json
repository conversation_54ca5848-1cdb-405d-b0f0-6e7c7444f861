{"name": "caguuutool", "type": "module", "version": "0.0.0", "private": true, "packageManager": "pnpm@9.15.0+sha512.76e2379760a4328ec4415815bcd6628dee727af3779aaa4c914e3944156c4299921a89f976381ee107d41f12cfa4b66681ca9c718f0668fa0831ed4c6d8ba56c", "scripts": {"dev": "vite", "build": "vue-tsc --noEmit && vite build", "build:dev": "vue-tsc --noEmit && vite build --mode development", "build:prod": "vue-tsc --noEmit && vite build --mode production", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build --force", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write src/"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@kuaitu/core": "workspace:^", "@types/fabric": "^5.3.0", "@types/file-saver": "^2.0.7", "@vueuse/core": "^10.1.0", "ag-psd": "^22.0.2", "axios": "^1.7.7", "chroma-js": "^3.1.2", "dayjs": "^1.11.13", "element-plus": "^2.8.8", "fabric": "^5.3.0", "file-saver": "^2.0.5", "ja": "link:element-plus/es/locale/lang/ja", "jsencrypt": "^3.3.2", "jwt-decode": "^4.0.0", "lodash-es": "^4.17.21", "pinia": "^2.2.6", "pinia-plugin-persistedstate": "^4.1.3", "vue": "^3.5.12", "vue-i18n": "^10.0.4", "vue-router": "^4.4.5"}, "workspaces": ["packages/*"], "devDependencies": {"@antfu/eslint-config": "^3.9.2", "@iconify-json/ep": "^1.2.1", "@tsconfig/node22": "^22.0.0", "@types/axios": "^0.14.4", "@types/chroma-js": "^2.4.4", "@types/lodash-es": "^4.17.12", "@types/node": "^22.9.0", "@vitejs/plugin-vue": "^5.1.4", "@vitejs/plugin-vue-jsx": "^4.0.1", "@vue/tsconfig": "^0.5.1", "eslint": "^9.15.0", "eslint-plugin-format": "^0.1.2", "npm-run-all2": "^7.0.1", "sass-embedded": "^1.81.0", "typescript": "~5.6.3", "unplugin-auto-import": "^0.18.4", "unplugin-icons": "^0.20.1", "unplugin-vue-components": "^0.27.4", "vite": "^5.4.10", "vite-plugin-vue-devtools": "^7.5.4", "vue-tsc": "^2.1.10"}}
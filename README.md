# CaguuuTool 图片翻译工具

欢迎使用CaguuuTool，这是一款专为商品信息翻译设计的图片翻译工具，旨在帮助您轻松地将中文商品信息翻译成日文，提升跨语言电商效率。

## 项目说明

1. **技术栈：**

   - Vue 3：前端框架，提供响应式界面开发
   - Vite：快速构建工具，支持热模块替换
   - TypeScript：增强代码类型安全和可维护性
   - Pinia：状态管理库，简化数据共享
   - Element Plus：UI组件库，提供丰富的界面元素
   - Fabric.js：强大的Canvas库，用于图片编辑
   - @kuaitu/core：Fabric.js 的 SDK 封装，定制化功能扩展
   - Vue I18n：国际化支持，适应多语言需求
   - Sass：CSS预处理器，优化样式编写

2. **功能概述：**
   CaguuuTool 是一款专注于图片翻译的工具，主要服务于将中文商品信息翻译为日文的场景，特别适合跨境电商和多语言内容创作者。其核心功能包括：

   - **商品图片编辑**：支持在图片上添加、编辑文本和图形
   - **文字翻译**：快速将中文内容翻译为日文，支持批量操作
   - **布局调整**：灵活调整图片和文本布局，适应不同展示需求
   - **批量处理**：支持多张图片同时编辑和翻译，提高效率
   - **模板管理**：保存和复用设计模板，简化重复工作

3. **主要页面：**
   项目包含以下主要页面，覆盖图片翻译和设计的完整流程：
   - **上传页面 (Upload)**：用于上传需要翻译或编辑的商品图片
   - **翻译校验页面 (Translate)**：检查和校对翻译内容，确保准确性
   - **设计页面 (Design)**：由设计上传设计图，提供设计和设计校验后的图片，使设计明白哪些设计图需要如何调整
   - **设计校验页面 (Design Check)**：验证设计结果，确认符合要求，不符合的设计图会被标记修改点并打回设计
   - **登录页面 (Login)**：用户身份验证入口

## 开发环境要求

- Node.js >= 16.0.0：确保开发和构建环境兼容
- pnpm >= 8.0.0：使用pnpm作为包管理工具，支持工作区管理

## 项目运行

```bash
# 安装依赖
pnpm install

# 开发环境运行
pnpm dev

# 生产环境构建
pnpm build

# 代码格式检查
pnpm lint
```

## 项目结构

```plaintext
├── dist                  # 构建输出目录
├── packages              # 子包目录
│   └── core             # 核心功能包，包含业务逻辑封装
├── public               # 静态资源目录，不会被打包处理
├── src                  # 源代码目录
│   ├── assets          # 项目资源，如图片、字体等
│   ├── components      # 可复用组件
│   ├── language        # 国际化文件，支持多语言
│   ├── stores         # Pinia状态管理模块
│   ├── styles         # 全局样式文件
│   ├── utils          # 工具函数和辅助代码
│   └── views          # 页面视图，包含路由页面
├── .vscode              # VSCode配置文件
├── auto-imports.d.ts    # 自动导入类型声明
├── components.d.ts      # 组件类型声明
├── vite.config.ts       # Vite配置文件
├── tsconfig.json        # TypeScript配置文件
└── index.html           # 项目入口HTML文件
```

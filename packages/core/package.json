{"name": "@kuaitu/core", "version": "1.0.0", "description": "", "main": "index.ts", "scripts": {"build": "vite build", "test": "vitest"}, "dependencies": {"@webtoon/psd": "^0.4.0", "events": "^3.3.0", "fabric-history": "^1.6.0", "fontfaceobserver": "^2.1.0", "hotkeys-js": "^3.8.8", "jsbarcode": "^3.11.6", "qr-code-styling": "1.6.0-rc.1", "qs": "^6.12.1", "tapable": "^2.2.1", "uuid": "^8.3.2"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@types/jsdom": "^21.1.6", "@types/qs": "^6.9.15", "jsdom": "^24.0.0", "vitest": "^1.6.0"}}